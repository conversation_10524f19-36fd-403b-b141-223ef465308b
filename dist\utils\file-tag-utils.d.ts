/**
 * File Tag Utilities
 *
 * Handles expansion and collapse of file tags (@file.txt) to XML blocks
 * for including file contents in AI context.
 */
/**
 * Expand file tags to XML blocks
 */
export declare function expandFileTags(text: string, workingDir?: string): Promise<string>;
/**
 * Collapse XML blocks back to file tags
 */
export declare function collapseXmlBlocks(text: string): string;
/**
 * Extract file paths from text
 */
export declare function extractFilePaths(text: string): string[];
/**
 * Extract file paths from XML blocks
 */
export declare function extractFilePathsFromXml(text: string): string[];
/**
 * Validate file tag syntax
 */
export declare function validateFileTags(text: string, workingDir?: string): {
    valid: boolean;
    errors: Array<{
        filePath: string;
        error: string;
    }>;
};
/**
 * Get file suggestions for auto-completion
 */
export declare function getFileSuggestions(input: string, workingDir?: string, maxSuggestions?: number): string[];
//# sourceMappingURL=file-tag-utils.d.ts.map