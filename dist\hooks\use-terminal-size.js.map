{"version": 3, "file": "use-terminal-size.js", "sourceRoot": "", "sources": ["../../src/hooks/use-terminal-size.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAEtC,MAAM,kBAAkB,GAAG,CAAC,CAAC;AAC7B,MAAM,kBAAkB,GAAG,CAAC,CAAC;AAO7B,MAAM,mBAAoB,SAAQ,YAAY;IACpC,WAAW,CAAe;IAC1B,SAAS,GAAsC,IAAI,GAAG,EAAE,CAAC;IACzD,aAAa,GAAwB,IAAI,CAAC;IAElD;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAsC;QAChD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE7B,0BAA0B;QAC1B,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC,GAAG,kBAAkB,CAAC;QACpE,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,kBAAkB,CAAC;QAE9D,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,gBAAgB;YAChD,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,EAAQ,iBAAiB;SAClD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE;YACxB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAElC,qCAAqC;YACrC,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO;gBAC5C,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBAC3C,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;gBAC3B,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;QAEF,2BAA2B;QAC3B,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEhD,oCAAoC;QACpC,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YACxD,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC;gBACH,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAEjD,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;gBACxD,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;CACF;AAED,+BAA+B;AAC/B,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAEtD;;GAEG;AACH,MAAM,UAAU,eAAe;IAK7B,MAAM,IAAI,GAAG,mBAAmB,CAAC,OAAO,EAAE,CAAC;IAE3C,OAAO;QACL,OAAO,EAAE,IAAI,CAAC,OAAO;QACrB,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,WAAW,EAAE,CAAC,QAAsC,EAAE,EAAE;YACtD,OAAO,mBAAmB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC;IACxD,MAAM,QAAQ,GAAG,mBAAmB,CAAC,OAAO,EAAE,CAAC;IAE/C,OAAO;QACL,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,GAAG,QAAQ,EAAE,EAAE,CAAC;QAClD,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,EAAE,CAAC,CAAC;KAC5C,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,UAAU,GAAG,EAAE;IAClD,OAAO,mBAAmB,CAAC,OAAO,EAAE,CAAC,OAAO,IAAI,UAAU,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,OAAO,GAAG,EAAE;IAC/C,OAAO,mBAAmB,CAAC,OAAO,EAAE,CAAC,IAAI,IAAI,OAAO,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB;IAM9B,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,mBAAmB,CAAC,OAAO,EAAE,CAAC;IAExD,MAAM,YAAY,GAAG,CAAC,CAAC;IACvB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtE,MAAM,UAAU,GAAG,IAAI,GAAG,WAAW,GAAG,YAAY,CAAC;IACrD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IAE5E,OAAO;QACL,UAAU;QACV,WAAW;QACX,YAAY;QACZ,YAAY;KACb,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB;IACjC,mBAAmB,CAAC,OAAO,EAAE,CAAC;AAChC,CAAC;AAED,0BAA0B;AAC1B,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;AACxC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;AAC1C,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;AAE3C,eAAe,mBAAmB,CAAC"}