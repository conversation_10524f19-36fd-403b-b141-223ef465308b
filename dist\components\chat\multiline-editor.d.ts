/**
 * Multiline Editor Component
 *
 * Advanced text editor with syntax highlighting, auto-completion,
 * undo/redo, and sophisticated cursor management.
 */
import blessed from 'blessed';
export interface MultilineEditorOptions {
    parent: blessed.Widgets.Node;
    top?: string | number;
    left?: string | number;
    width?: string | number;
    height?: string | number;
    border?: boolean;
    style?: any;
    placeholder?: string;
    maxLines?: number;
    tabSize?: number;
    wordWrap?: boolean;
    showLineNumbers?: boolean;
    syntaxHighlighting?: boolean;
    autoComplete?: boolean;
    onSubmit?: (text: string) => void;
    onCancel?: () => void;
    onChange?: (text: string) => void;
    onCursorMove?: (row: number, col: number) => void;
}
export declare class MultilineEditor {
    private element;
    private textBuffer;
    private options;
    private isActive;
    private placeholder;
    private maxLines;
    private showLineNumbers;
    private lineNumberWidth;
    constructor(options: MultilineEditorOptions);
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * <PERSON><PERSON> enter key press
     */
    private handleEnter;
    /**
     * <PERSON>le mouse click
     */
    private handleMouseClick;
    /**
     * Update visual display
     */
    private updateDisplay;
    /**
     * Notify change event
     */
    private notifyChange;
    /**
     * Notify cursor move event
     */
    private notifyCursorMove;
    /**
     * Get current text content
     */
    getText(): string;
    /**
     * Set text content
     */
    setText(text: string): void;
    /**
     * Clear all content
     */
    clear(): void;
    /**
     * Focus the editor
     */
    focus(): void;
    /**
     * Check if editor is empty
     */
    isEmpty(): boolean;
    /**
     * Get cursor position
     */
    getCursorPosition(): {
        row: number;
        col: number;
    };
    /**
     * Get line count
     */
    getLineCount(): number;
    /**
     * Get the underlying blessed element
     */
    getElement(): blessed.Widgets.BoxElement;
    /**
     * Destroy the editor
     */
    destroy(): void;
}
export default MultilineEditor;
//# sourceMappingURL=multiline-editor.d.ts.map