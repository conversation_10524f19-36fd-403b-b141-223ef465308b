/**
 * Sessions Overlay Component
 *
 * Manages saved conversation sessions with search, load, and delete functionality.
 */
import blessed from 'blessed';
import type { SessionData } from '../../types/index.js';
export interface SessionsOverlayOptions {
    parent: blessed.Widgets.Screen;
    onLoad?: (sessionData: SessionData) => void;
    onClose?: () => void;
}
export declare class SessionsOverlay {
    private screen;
    private container;
    private sessionsList;
    private detailsBox;
    private searchBox;
    private statusBar;
    private sessions;
    private filteredSessions;
    private searchTerm;
    private options;
    private sessionsDir;
    constructor(options: SessionsOverlayOptions);
    /**
     * Load available sessions
     */
    private loadSessions;
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Filter sessions based on search term
     */
    private filterSessions;
    /**
     * Update display
     */
    private updateDisplay;
    /**
     * Update sessions list
     */
    private updateSessionsList;
    /**
     * Update details display
     */
    private updateDetails;
    /**
     * Format duration in human readable format
     */
    private formatDuration;
    /**
     * Load selected session
     */
    private loadSelected;
    /**
     * Delete selected session
     */
    private deleteSelected;
    /**
     * Refresh sessions list
     */
    private refresh;
    /**
     * Show the overlay
     */
    show(): void;
    /**
     * Hide the overlay
     */
    hide(): void;
    /**
     * Close the overlay
     */
    close(): void;
    /**
     * Destroy the overlay
     */
    destroy(): void;
}
export default SessionsOverlay;
//# sourceMappingURL=sessions-overlay.d.ts.map