/**
 * Custom Spinner Component
 *
 * Provides animated spinner widgets with different animation types
 * and configurable speed for loading indicators.
 */
import blessed from 'blessed';
const spinnerTypes = {
    dots: ["⢎ ", "⠎⠁", "⠊⠑", "⠈⠱", " ⡱", "⢀⡰", "⢄⡠", "⢆⡀"],
    ball: [
        "( ●    )",
        "(  ●   )",
        "(   ●  )",
        "(    ● )",
        "(     ●)",
        "(    ● )",
        "(   ●  )",
        "(  ●   )",
        "( ●    )",
        "(●     )",
    ],
    line: ["|", "/", "-", "\\"],
    bounce: ["⠁", "⠂", "⠄", "⡀", "⢀", "⠠", "⠐", "⠈"],
};
export class Spinner {
    element;
    frames;
    currentFrame = 0;
    interval = null;
    speed;
    isSpinning = false;
    constructor(options) {
        this.speed = options.speed || 100;
        this.frames = spinnerTypes[options.type || 'dots'] || spinnerTypes.dots;
        this.element = blessed.box({
            parent: options.parent,
            top: options.top || 0,
            left: options.left || 0,
            width: options.width || 'shrink',
            height: options.height || 1,
            content: this.frames[0],
            style: {
                fg: 'cyan',
                ...options.style,
            },
            hidden: options.hidden,
            tags: false,
        });
    }
    /**
     * Start spinning animation
     */
    start() {
        if (this.isSpinning) {
            return;
        }
        this.isSpinning = true;
        this.element.show();
        this.interval = setInterval(() => {
            this.currentFrame = (this.currentFrame + 1) % this.frames.length;
            this.element.setContent(this.frames[this.currentFrame]);
            if (this.element.screen) {
                this.element.screen.render();
            }
        }, this.speed);
    }
    /**
     * Stop spinning animation
     */
    stop() {
        if (!this.isSpinning) {
            return;
        }
        this.isSpinning = false;
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        this.element.hide();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    /**
     * Set spinner text
     */
    setText(text) {
        const frame = this.frames[this.currentFrame];
        this.element.setContent(`${frame} ${text}`);
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    /**
     * Change spinner type
     */
    setType(type) {
        this.frames = spinnerTypes[type] || spinnerTypes.dots;
        this.currentFrame = 0;
        if (this.isSpinning) {
            this.element.setContent(this.frames[0]);
            if (this.element.screen) {
                this.element.screen.render();
            }
        }
    }
    /**
     * Change spinner speed
     */
    setSpeed(speed) {
        this.speed = speed;
        if (this.isSpinning) {
            this.stop();
            this.start();
        }
    }
    /**
     * Get the underlying blessed element
     */
    getElement() {
        return this.element;
    }
    /**
     * Check if spinner is currently spinning
     */
    isActive() {
        return this.isSpinning;
    }
    /**
     * Destroy the spinner
     */
    destroy() {
        this.stop();
        this.element.destroy();
    }
    /**
     * Show the spinner element
     */
    show() {
        this.element.show();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    /**
     * Hide the spinner element
     */
    hide() {
        this.element.hide();
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    /**
     * Set position
     */
    setPosition(top, left) {
        this.element.top = top;
        this.element.left = left;
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
    /**
     * Set size
     */
    setSize(width, height) {
        this.element.width = width;
        this.element.height = height;
        if (this.element.screen) {
            this.element.screen.render();
        }
    }
}
/**
 * Create a spinner with text
 */
export function createSpinnerWithText(parent, text, options = {}) {
    const spinner = new Spinner({
        parent,
        type: 'dots',
        ...options,
    });
    spinner.setText(text);
    return spinner;
}
/**
 * Create a loading spinner
 */
export function createLoadingSpinner(parent, position = { top: 'center', left: 'center' }) {
    return new Spinner({
        parent,
        top: position.top,
        left: position.left,
        type: 'ball',
        speed: 80,
        style: {
            fg: 'yellow',
        },
    });
}
export default Spinner;
//# sourceMappingURL=spinner.js.map