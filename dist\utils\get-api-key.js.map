{"version": 3, "file": "get-api-key.js", "sourceRoot": "", "sources": ["../../src/utils/get-api-key.tsx"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,EAAE,SAAS,EAA0B,MAAM,aAAa,CAAC;AAChE,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAC/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAerD;;GAEG;AACH,MAAM,OAAO,WAAW;IACd,MAAM,CAAyB;IAC/B,SAAS,CAA6B;IACtC,YAAY,CAA8B;IAC1C,QAAQ,CAAiC;IACzC,SAAS,CAA6B;IACtC,eAAe,CAA6B;IAE5C,SAAS,GAAa,EAAE,CAAC;IACzB,gBAAgB,GAAG,EAAE,CAAC;IACtB,UAAU,CAAuC;IAEzD;QACE,IAAI,CAAC,SAAS,GAAG,gBAAgB,EAAE,CAAC;QACpC,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAC3B,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,6BAA6B;SACrC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,iBAAiB;QACjB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,KAAK;YACb,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,MAAM;iBACX;aACF;YACD,KAAK,EAAE,iBAAiB;YACxB,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;QAEH,eAAe;QACf,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC;YACjC,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,IAAI,EAAE,IAAI;YACV,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO;aACZ;SACF,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;YAC/B,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,QAAQ;iBACb;gBACD,QAAQ,EAAE;oBACR,EAAE,EAAE,MAAM;iBACX;aACF;YACD,KAAK,EAAE,mBAAmB;YAC1B,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,IAAI;YACR,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBAC5B,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;gBAChC,MAAM,MAAM,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBAClC,OAAO,GAAG,MAAM,IAAI,QAAQ,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;YAC5C,CAAC,CAAC;SACH,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;YAC9B,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,CAAC;YACT,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,OAAO;iBACZ;aACF;YACD,KAAK,EAAE,WAAW;YAClB,YAAY,EAAE,IAAI;YAClB,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,IAAI,EAAE,0BAA0B;SACzC,CAAC,CAAC;QAEH,aAAa;QACb,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;YAC3B,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,MAAM;iBACX;aACF;YACD,KAAK,EAAE,UAAU;YACjB,UAAU,EAAE,IAAI;YAChB,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;YAC5B,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,+DAA+D;YACxE,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM;aACX;YACD,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,kBAAkB;QAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE;YAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YAC5B,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE;YAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE;YACxC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;YACtC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE;YAC3C,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,OAAO;YACL,+CAA+C;YAC/C,EAAE;YACF,0DAA0D;YAC1D,wDAAwD;YACxD,oDAAoD;SACrD,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;QAC5C,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACtD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;QAC5C,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACtD,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;YAC3C,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;YAE3C,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,OAAO,GAAG;oBACd,0BAA0B,QAAQ,CAAC,IAAI,EAAE;oBACzC,sCAAsC,QAAQ,CAAC,MAAM,EAAE;oBACvD,0BAA0B,QAAQ,CAAC,OAAO,EAAE;oBAC5C,gCAAgC,UAAU,CAAC,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAC,2BAA2B,EAAE;oBAC7G,EAAE;oBACF,4BAA4B;oBAC5B,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;iBAC3C,CAAC;gBAEF,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,QAAgB;QAC9C,QAAQ,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YAC/B,KAAK,QAAQ;gBACX,OAAO,6DAA6D,CAAC;YACvE,KAAK,WAAW;gBACd,OAAO,uDAAuD,CAAC;YACjE,KAAK,QAAQ;gBACX,OAAO,iEAAiE,CAAC;YAC3E,KAAK,SAAS;gBACZ,OAAO,oDAAoD,CAAC;YAC9D,KAAK,QAAQ;gBACX,OAAO,2CAA2C,CAAC;YACrD;gBACE,OAAO,wDAAwD,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS,CAAC,MAAc;QACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,YAAY,CAAC,gDAAgD,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,2BAA2B;YAC3B,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,GAAG,MAAM,CAAC;YAErH,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YAExC,mBAAmB;YACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEzE,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,YAAY,CAAC,sDAAsD,CAAC,CAAC;gBAC1E,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAE1B,iBAAiB;gBACjB,UAAU,CAAC,GAAG,EAAE;oBACd,IAAI,CAAC,QAAQ,CAAC;wBACZ,OAAO,EAAE,IAAI;wBACb,QAAQ,EAAE,IAAI,CAAC,gBAAgB;wBAC/B,WAAW,EAAE,IAAI;qBAClB,CAAC,CAAC;gBACL,CAAC,EAAE,IAAI,CAAC,CAAC;YACX,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,sEAAsE,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YACzC,IAAI,CAAC,YAAY,CAAC,kBAAkB,KAAK,CAAC,OAAO,WAAW,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,YAAY,CAAC,iCAAiC,CAAC,CAAC;YACrD,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAChD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,CAAC,mCAAmC,CAAC,CAAC;YACvD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAExC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEzE,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,YAAY,CAAC,sDAAsD,CAAC,CAAC;YAC5E,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,+CAA+C,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,kCAAkC,KAAK,CAAC,OAAO,WAAW,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,MAAc;QAC3D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,kBAAkB,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YAExD,6BAA6B;YAC7B,IAAI,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE,CAAC;gBACxC,+CAA+C;gBAC/C,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,yDAAyD;gBACzD,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBACnC,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,EAAE,YAAY,IAAI,eAAe;oBAC7D,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;oBAC7C,UAAU,EAAE,CAAC;iBACd,CAAC,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,iCAAiC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,OAAe;QAClC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEzC,iCAAiC;QACjC,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QACtE,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;YACrB,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QAChC,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,wBAAwB,OAAO,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YACnC,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,MAAM,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAClC,OAAO,GAAG,MAAM,IAAI,QAAQ,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,MAAyB;QACxC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAEtB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,IAAI;QACT,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAAC,QAAgB,EAAE,MAAe;IACpE,MAAM,GAAG,GAAG,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,kBAAkB,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;QAE7D,IAAI,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE,CAAC;YACxC,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACnC,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,EAAE,YAAY,IAAI,eAAe;gBAC7D,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;gBAC7C,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,UAA8B,EAAE;IAChE,MAAM,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;IAChC,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;AACtB,CAAC;AAED,eAAe,WAAW,CAAC"}