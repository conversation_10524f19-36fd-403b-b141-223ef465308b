{"version": 3, "file": "cli-singlepass.js", "sourceRoot": "", "sources": ["../src/cli-singlepass.tsx"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC;AACzD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,MAAM,CAAC;AAC/C,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,UAAU,EAAE,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAW,QAAQ,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAEvE,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAYvE;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,OAAyB;IAC3D,MAAM,KAAK,GAAG,WAAW,CAAC,uBAAuB,CAAC,CAAC;IAEnD,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAClF,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,sBAAsB;QACtB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,MAAM,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAE/C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,gBAAgB;QAChB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACpD,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAEhE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,oBAAoB,OAAO,CAAC,MAAM,cAAc,CAAC,CAAC,CAAC;QAC3E,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,kBAAkB;QAClB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,gBAAgB;QAChB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE9D,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACxE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC;QAED,KAAK,CAAC,GAAG,EAAE,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,KAAK,CAAC,GAAG,EAAE,CAAC;QACZ,MAAM,GAAG,GAAG,KAAc,CAAC;QAC3B,QAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;QACzC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,iBAAiB,CAAC,OAAyB;IACxD,MAAM,KAAK,GAAkB,EAAE,CAAC;IAChC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC;IACzC,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM;IAChE,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI;QACjD,cAAc;QACd,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;KACV,CAAC;IAEF,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,SAAS,aAAa,CAAC,QAAgB;QACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE1D,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACpC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBACvD,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAClC,CAAC;YACD,OAAO,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,UAAU,CAAC,QAAgB;QAClC,MAAM,cAAc,GAAG;YACrB,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;YACxE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO;YACtE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;YACnE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;YACvE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;YAClE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,gBAAgB;SAC9D,CAAC;QAEF,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,OAAO,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,mCAAmC;IAClF,CAAC;IAED,SAAS,aAAa,CAAC,OAAe;QACpC,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,IAAI,SAAS,IAAI,YAAY,EAAE,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;YAErC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAEtC,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC5B,SAAS;gBACX,CAAC;gBAED,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEjC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;oBACxB,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAC1B,CAAC;qBAAM,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAClD,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,+BAA+B;wBAC5D,SAAS;oBACX,CAAC;oBAED,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,GAAG,YAAY,EAAE,CAAC;wBAC1C,SAAS;oBACX,CAAC;oBAED,IAAI,CAAC;wBACH,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;wBAC/C,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;wBAE1D,KAAK,CAAC,IAAI,CAAC;4BACT,IAAI,EAAE,YAAY;4BAClB,OAAO;4BACP,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,QAAQ,EAAE,YAAY;yBACvB,CAAC,CAAC;wBAEH,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC;oBAC1B,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,gCAAgC;wBAChC,QAAQ,CAAC,wBAAwB,QAAQ,EAAE,EAAE,KAAc,CAAC,CAAC;oBAC/D,CAAC;gBACH,CAAC;gBAED,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;oBAC7B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,6BAA6B,OAAO,EAAE,EAAE,KAAc,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAEhC,2DAA2D;IAC3D,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAClB,MAAM,aAAa,GAAG,CAAC,IAAiB,EAAU,EAAE;YAClD,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAErC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAAE,OAAO,EAAE,CAAC;YACxE,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAAE,OAAO,CAAC,CAAC;YAC3D,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAAE,OAAO,CAAC,CAAC;YAC3D,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAAE,OAAO,CAAC,CAAC;YAC5C,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAAE,OAAO,CAAC,CAAC;YACvD,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QAEF,OAAO,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,MAAc,EAAE,KAAoB;IAC5D,MAAM,YAAY,GAAa,EAAE,CAAC;IAElC,oBAAoB;IACpB,YAAY,CAAC,IAAI,CAAC,yGAAyG,CAAC,CAAC;IAC7H,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEtB,kBAAkB;IAClB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACrC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEtB,yBAAyB;IACzB,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAC3C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACnB,YAAY,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IACH,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEtB,oBAAoB;IACpB,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACtC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACnB,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACtC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACrC,YAAY,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;IACtF,YAAY,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;IAC/G,YAAY,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;IAE3F,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,aAAa,CAAC,OAAe,EAAE,MAAiB;IAC7D,MAAM,MAAM,GAAG,kBAAkB,CAAC;QAChC,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,OAAO,EAAE,MAAM,EAAE,mCAAmC;KACrD,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,OAAO;iBACjB;aACF;YACD,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,GAAG,EAAE,iDAAiD;SACpE,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;QACtD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,QAAQ,CAAC,sBAAsB,EAAE,KAAc,CAAC,CAAC;QACjD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,YAAY,CAAC,QAAgB,EAAE,QAAgB;IAC5D,IAAI,CAAC;QACH,8BAA8B;QAC9B,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAE/D,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,oCAAoC;QACpC,MAAM,aAAa,GAAG,MAAM,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEtE,OAAO,aAAa,CAAC;IAEvB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,GAAG,GAAG,KAAc,CAAC;QAC3B,QAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;QACzC,OAAO;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC;YACrB,aAAa,EAAE,EAAE;YACjB,YAAY,EAAE,EAAE;YAChB,YAAY,EAAE,EAAE;SACjB,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,wBAAwB,CAAC,MAAwB;IAC/D,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,GAAG,IAAI,EAAE,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}