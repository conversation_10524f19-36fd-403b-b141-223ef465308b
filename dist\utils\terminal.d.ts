/**
 * Terminal Management Utilities
 *
 * Handles terminal state management, screen clearing, cursor control,
 * and integration with the blessed.js renderer.
 */
import blessed from 'blessed';
/**
 * Set the current blessed renderer
 */
export declare function setRenderer(renderer: blessed.Widgets.Screen): void;
/**
 * Get the current renderer
 */
export declare function getRenderer(): blessed.Widgets.Screen | null;
/**
 * Restore terminal to original state
 */
export declare function restoreTerminal(): void;
/**
 * Clear terminal screen and scrollback
 */
export declare function clearTerminal(): void;
/**
 * Show cursor
 */
export declare function showCursor(): void;
/**
 * Hide cursor
 */
export declare function hideCursor(): void;
/**
 * Move cursor to position
 */
export declare function moveCursor(x: number, y: number): void;
/**
 * Get terminal size
 */
export declare function getTerminalSize(): {
    width: number;
    height: number;
};
/**
 * Check if terminal supports colors
 */
export declare function supportsColor(): boolean;
/**
 * Check if terminal supports Unicode
 */
export declare function supportsUnicode(): boolean;
/**
 * Enable raw mode for input
 */
export declare function enableRawMode(): void;
/**
 * Disable raw mode for input
 */
export declare function disableRawMode(): void;
/**
 * Start FPS debugging for UI performance
 */
export declare function startFPSDebugging(): void;
/**
 * Handle terminal resize events
 */
export declare function onTerminalResize(callback: (width: number, height: number) => void): () => void;
/**
 * Setup graceful exit handling
 */
export declare function onExit(): void;
/**
 * Initialize terminal management
 */
export declare function initializeTerminal(): void;
/**
 * Check if terminal is interactive
 */
export declare function isInteractive(): boolean;
/**
 * Get terminal capabilities
 */
export declare function getTerminalCapabilities(): {
    colors: boolean;
    unicode: boolean;
    mouse: boolean;
    interactive: boolean;
    size: {
        width: number;
        height: number;
    };
};
//# sourceMappingURL=terminal.d.ts.map