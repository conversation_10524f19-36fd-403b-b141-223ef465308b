/**
 * Version Management for Kritrima AI CLI
 *
 * Provides dynamic version loading from package.json
 */
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
let cachedVersion = null;
/**
 * Get the current CLI version from package.json
 */
export function getVersion() {
    if (cachedVersion) {
        return cachedVersion;
    }
    try {
        // Try to read from package.json in the project root
        const packageJsonPath = join(__dirname, '..', 'package.json');
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
        cachedVersion = packageJson.version || 'unknown';
        return cachedVersion;
    }
    catch (error) {
        console.warn('Warning: Could not read version from package.json:', error);
        return 'unknown';
    }
}
/**
 * CLI version constant for easy access
 */
export const CLI_VERSION = getVersion();
/**
 * Get version information including build details
 */
export function getVersionInfo() {
    return {
        version: CLI_VERSION,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
    };
}
/**
 * Format version information for display
 */
export function formatVersionInfo() {
    const info = getVersionInfo();
    return [
        `Kritrima AI CLI v${info.version}`,
        `Node.js ${info.nodeVersion}`,
        `Platform: ${info.platform}-${info.arch}`,
    ].join('\n');
}
// Additional constants and features
export const CLI_NAME = 'Kritrima AI CLI';
export const CLI_DESCRIPTION = 'Advanced AI-powered command line interface';
export const BUILD_DATE = new Date().toISOString();
export const BUILD_COMMIT = process.env.GIT_COMMIT || 'unknown';
export const BUILD_BRANCH = process.env.GIT_BRANCH || 'main';
// Feature flags
export const FEATURES = {
    MULTILINE_INPUT: true,
    FILE_TAG_EXPANSION: true,
    SLASH_COMMANDS: true,
    COMMAND_APPROVAL: true,
    SYNTAX_HIGHLIGHTING: true,
    AUTO_COMPLETION: true,
    HISTORY_MANAGEMENT: true,
    SESSION_PERSISTENCE: true,
    DESKTOP_NOTIFICATIONS: true,
    GIT_INTEGRATION: true,
    SANDBOX_EXECUTION: true,
    STREAMING_RESPONSES: true,
    DIFF_VIEWER: true,
    BUG_REPORTING: true,
    UPDATE_CHECKING: true,
};
// API compatibility
export const API_VERSION = '1.0';
export const MIN_NODE_VERSION = '18.0.0';
export const SUPPORTED_PLATFORMS = ['win32', 'darwin', 'linux'];
// Repository information
export const REPOSITORY = {
    URL: 'https://github.com/kritrima/kritrima-ai-cli',
    ISSUES_URL: 'https://github.com/kritrima/kritrima-ai-cli/issues',
    DOCS_URL: 'https://docs.kritrima.ai/cli',
    CHANGELOG_URL: 'https://github.com/kritrima/kritrima-ai-cli/blob/main/CHANGELOG.md',
};
// License information
export const LICENSE = 'MIT';
export const COPYRIGHT = `© ${new Date().getFullYear()} Kritrima AI`;
/**
 * Get full version string
 */
export function getVersionString() {
    return `${CLI_NAME} v${CLI_VERSION}`;
}
/**
 * Get detailed version information
 */
export function getDetailedVersionInfo() {
    return {
        name: CLI_NAME,
        version: CLI_VERSION,
        description: CLI_DESCRIPTION,
        buildDate: BUILD_DATE,
        buildCommit: BUILD_COMMIT,
        buildBranch: BUILD_BRANCH,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
    };
}
/**
 * Get feature availability
 */
export function getFeatures() {
    return { ...FEATURES };
}
/**
 * Check if feature is enabled
 */
export function isFeatureEnabled(feature) {
    return FEATURES[feature] === true;
}
/**
 * Get platform compatibility info
 */
export function getPlatformInfo() {
    const currentPlatform = process.platform;
    const supported = SUPPORTED_PLATFORMS.includes(currentPlatform);
    return {
        supported,
        platform: currentPlatform,
        arch: process.arch,
        nodeVersion: process.version,
        minNodeVersion: MIN_NODE_VERSION,
    };
}
/**
 * Check Node.js version compatibility
 */
export function checkNodeCompatibility() {
    const current = process.version;
    const currentMajor = parseInt(current.slice(1).split('.')[0]);
    const minimumMajor = parseInt(MIN_NODE_VERSION.split('.')[0]);
    const compatible = currentMajor >= minimumMajor;
    return {
        compatible,
        current,
        minimum: MIN_NODE_VERSION,
        message: compatible ?
            undefined :
            `Node.js ${MIN_NODE_VERSION} or higher is required. Current: ${current}`,
    };
}
/**
 * Get CLI banner
 */
export function getBanner() {
    return `
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║   ██╗  ██╗██████╗ ██╗████████╗██████╗ ██╗███╗   ███╗ █████╗  ║
║   ██║ ██╔╝██╔══██╗██║╚══██╔══╝██╔══██╗██║████╗ ████║██╔══██╗ ║
║   █████╔╝ ██████╔╝██║   ██║   ██████╔╝██║██╔████╔██║███████║ ║
║   ██╔═██╗ ██╔══██╗██║   ██║   ██╔══██╗██║██║╚██╔╝██║██╔══██║ ║
║   ██║  ██╗██║  ██║██║   ██║   ██║  ██║██║██║ ╚═╝ ██║██║  ██║ ║
║   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝ ║
║                                                              ║
║                    AI CLI v${CLI_VERSION.padEnd(8)}                    ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
`;
}
//# sourceMappingURL=version.js.map