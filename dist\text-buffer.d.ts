/**
 * Sophisticated Text Buffer System
 *
 * Provides advanced multi-line text editing capabilities with Unicode support,
 * undo/redo functionality, cursor management, and viewport handling.
 */
export interface UndoState {
    lines: string[];
    cursorRow: number;
    cursorCol: number;
    scrollRow: number;
    scrollCol: number;
    version: number;
}
export interface TextBufferOptions {
    maxUndoStates?: number;
    tabSize?: number;
    wrapMode?: 'none' | 'word' | 'char';
}
export default class TextBuffer {
    private lines;
    private cursorRow;
    private cursorCol;
    private scrollRow;
    private scrollCol;
    private version;
    private undoStack;
    private redoStack;
    private maxUndoStates;
    private tabSize;
    private wrapMode;
    private viewportWidth;
    private viewportHeight;
    constructor(options?: TextBufferOptions);
    /**
     * Set viewport dimensions
     */
    setViewport(width: number, height: number): void;
    /**
     * Get current text content
     */
    getText(): string;
    /**
     * Set text content
     */
    setText(text: string): void;
    /**
     * Insert text at cursor position
     */
    insertText(text: string): void;
    /**
     * Delete character at cursor position
     */
    deleteChar(): void;
    /**
     * Delete character after cursor position
     */
    deleteCharForward(): void;
    /**
     * Insert new line at cursor position
     */
    insertNewLine(): void;
    /**
     * Move cursor to position
     */
    moveCursor(row: number, col: number): void;
    /**
     * Move cursor by offset
     */
    moveCursorBy(deltaRow: number, deltaCol: number): void;
    /**
     * Move cursor to beginning of line
     */
    moveToLineStart(): void;
    /**
     * Move cursor to end of line
     */
    moveToLineEnd(): void;
    /**
     * Move cursor to beginning of buffer
     */
    moveToBufferStart(): void;
    /**
     * Move cursor to end of buffer
     */
    moveToBufferEnd(): void;
    /**
     * Move cursor by word
     */
    moveWordForward(): void;
    /**
     * Move cursor backward by word
     */
    moveWordBackward(): void;
    /**
     * Get cursor position
     */
    getCursorPosition(): {
        row: number;
        col: number;
    };
    /**
     * Get scroll position
     */
    getScrollPosition(): {
        row: number;
        col: number;
    };
    /**
     * Ensure cursor is visible in viewport
     */
    private ensureCursorVisible;
    /**
     * Save current state for undo
     */
    private saveUndoState;
    /**
     * Undo last operation
     */
    undo(): boolean;
    /**
     * Redo last undone operation
     */
    redo(): boolean;
    /**
     * Get visible lines for rendering
     */
    getVisibleLines(): string[];
    /**
     * Get line count
     */
    getLineCount(): number;
    /**
     * Get line at index
     */
    getLine(index: number): string;
    /**
     * Delete current line
     */
    deleteLine(): void;
    /**
     * Delete from cursor to end of line
     */
    deleteToLineEnd(): void;
    /**
     * Delete word at cursor
     */
    deleteWord(): void;
    /**
     * Get buffer statistics
     */
    getStats(): {
        lines: number;
        characters: number;
        words: number;
        version: number;
    };
    /**
     * Clear all content
     */
    clear(): void;
    /**
     * Check if buffer is empty
     */
    isEmpty(): boolean;
    /**
     * Get current version
     */
    getVersion(): number;
}
//# sourceMappingURL=text-buffer.d.ts.map