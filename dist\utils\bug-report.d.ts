/**
 * Bug Report Generation
 *
 * Generates pre-filled GitHub issue URLs with session data,
 * system information, and reproduction steps.
 */
import type { ResponseItem, ResponseOutputItem } from '../types/index.js';
export interface BugReportData {
    items: Array<ResponseItem | ResponseOutputItem>;
    cliVersion: string;
    model: string;
    provider: string;
    platform: string;
    nodeVersion: string;
    error?: Error;
    additionalContext?: string;
}
/**
 * Build GitHub issue URL with bug report data
 */
export declare function buildBugReportUrl(data: BugReportData): string;
/**
 * Get system information for bug reports
 */
export declare function getSystemInfo(): {
    platform: string;
    nodeVersion: string;
    cliVersion: string;
};
/**
 * Create bug report data from current session
 */
export declare function createBugReportData(items: Array<ResponseItem | ResponseOutputItem>, model: string, provider: string, error?: Error, additionalContext?: string): BugReportData;
/**
 * Generate quick bug report URL
 */
export declare function generateQuickBugReport(error: Error, context?: string): string;
//# sourceMappingURL=bug-report.d.ts.map