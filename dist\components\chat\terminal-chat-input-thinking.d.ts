/**
 * Terminal Chat Input Thinking Animation
 *
 * Displays animated thinking indicator while AI is processing,
 * with customizable messages and progress indication.
 */
import blessed from 'blessed';
export interface ThinkingAnimationOptions {
    parent: blessed.Widgets.Node;
    top?: string | number;
    left?: string | number;
    width?: string | number;
    height?: string | number;
    messages?: string[];
    cycleInterval?: number;
    showProgress?: boolean;
}
export declare class TerminalChatInputThinking {
    private container;
    private spinner;
    private messageBox;
    private progressBox?;
    private options;
    private messages;
    private currentMessageIndex;
    private messageInterval;
    private isActive;
    private startTime;
    private progressInterval;
    constructor(options: ThinkingAnimationOptions);
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Start thinking animation
     */
    start(initialMessage?: string): void;
    /**
     * Stop thinking animation
     */
    stop(): void;
    /**
     * Update thinking message
     */
    setMessage(message: string): void;
    /**
     * Add a new message to the cycle
     */
    addMessage(message: string): void;
    /**
     * Set progress text
     */
    setProgress(text: string): void;
    /**
     * Start message cycling
     */
    private startMessageCycling;
    /**
     * Start progress updates
     */
    private startProgressUpdates;
    /**
     * Render the component
     */
    private render;
    /**
     * Check if animation is active
     */
    isThinking(): boolean;
    /**
     * Get elapsed time in milliseconds
     */
    getElapsedTime(): number;
    /**
     * Set spinner type
     */
    setSpinnerType(type: 'dots' | 'ball' | 'line' | 'bounce'): void;
    /**
     * Set spinner speed
     */
    setSpinnerSpeed(speed: number): void;
    /**
     * Show the thinking animation
     */
    show(): void;
    /**
     * Hide the thinking animation
     */
    hide(): void;
    /**
     * Get the container element
     */
    getElement(): blessed.Widgets.BoxElement;
    /**
     * Destroy the component
     */
    destroy(): void;
}
/**
 * Create a thinking animation with default settings
 */
export declare function createThinkingAnimation(parent: blessed.Widgets.Node, options?: Partial<ThinkingAnimationOptions>): TerminalChatInputThinking;
/**
 * Create a simple thinking indicator
 */
export declare function createSimpleThinking(parent: blessed.Widgets.Node, message?: string): TerminalChatInputThinking;
/**
 * Create thinking animation with custom messages
 */
export declare function createCustomThinking(parent: blessed.Widgets.Node, messages: string[], options?: Partial<ThinkingAnimationOptions>): TerminalChatInputThinking;
export default TerminalChatInputThinking;
//# sourceMappingURL=terminal-chat-input-thinking.d.ts.map