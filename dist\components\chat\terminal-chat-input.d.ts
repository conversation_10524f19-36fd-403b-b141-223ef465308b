/**
 * Advanced Terminal Chat Input Component
 *
 * Sophisticated input system with multi-line editing, auto-completion,
 * file tag expansion, slash commands, and history management.
 */
import blessed from 'blessed';
export interface ChatInputOptions {
    parent: blessed.Widgets.Node;
    top?: string | number;
    left?: string | number;
    width?: string | number;
    height?: string | number;
    maxHeight?: number;
    placeholder?: string;
    onSubmit?: (text: string) => void;
    onCancel?: () => void;
    onSlashCommand?: (command: string, args: string[]) => void;
    onHeightChange?: (height: number) => void;
    workingDirectory?: string;
}
export interface AutoCompleteItem {
    text: string;
    description?: string;
    type: 'file' | 'command' | 'history';
    insertText?: string;
}
export declare class TerminalChatInput {
    private container;
    private editor;
    private statusBar;
    private autoCompleteList;
    private options;
    private history;
    private historyIndex;
    private autoCompleteItems;
    private autoCompleteVisible;
    private autoCompleteIndex;
    private currentHeight;
    private workingDirectory;
    constructor(options: ChatInputOptions);
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Handle text submission
     */
    private handleSubmit;
    /**
     * Handle cancellation
     */
    private handleCancel;
    /**
     * Handle text change
     */
    private handleTextChange;
    /**
     * Handle tab completion
     */
    private handleTabCompletion;
    /**
     * Update auto-complete suggestions
     */
    private updateAutoComplete;
    /**
     * Show auto-complete list
     */
    private showAutoComplete;
    /**
     * Hide auto-complete list
     */
    private hideAutoComplete;
    /**
     * Update auto-complete display
     */
    private updateAutoCompleteDisplay;
    /**
     * Position auto-complete list
     */
    private positionAutoComplete;
    /**
     * Navigate auto-complete list
     */
    private navigateAutoComplete;
    /**
     * Accept current auto-complete suggestion
     */
    private acceptAutoComplete;
    /**
     * Navigate command history
     */
    private navigateHistory;
    /**
     * Add text to history
     */
    private addToHistory;
    /**
     * Update input height based on content
     */
    private updateHeight;
    /**
     * Get current text
     */
    getText(): string;
    /**
     * Set text content
     */
    setText(text: string): void;
    /**
     * Clear input
     */
    clear(): void;
    /**
     * Focus the input
     */
    focus(): void;
    /**
     * Check if input is empty
     */
    isEmpty(): boolean;
    /**
     * Set working directory
     */
    setWorkingDirectory(directory: string): void;
    /**
     * Get the container element
     */
    getElement(): blessed.Widgets.BoxElement;
    /**
     * Destroy the input component
     */
    destroy(): void;
}
export default TerminalChatInput;
//# sourceMappingURL=terminal-chat-input.d.ts.map