/**
 * Multiline Editor Component
 * 
 * Advanced text editor with syntax highlighting, auto-completion,
 * undo/redo, and sophisticated cursor management.
 */

import blessed from 'blessed';
import TextBuffer from '../../text-buffer.js';
import { useTerminalSize } from '../../hooks/use-terminal-size.js';
import { logDebug, logError } from '../../utils/logger/log.js';

export interface MultilineEditorOptions {
  parent: blessed.Widgets.Node;
  top?: string | number;
  left?: string | number;
  width?: string | number;
  height?: string | number;
  border?: boolean;
  style?: any;
  placeholder?: string;
  maxLines?: number;
  tabSize?: number;
  wordWrap?: boolean;
  showLineNumbers?: boolean;
  syntaxHighlighting?: boolean;
  autoComplete?: boolean;
  onSubmit?: (text: string) => void;
  onCancel?: () => void;
  onChange?: (text: string) => void;
  onCursorMove?: (row: number, col: number) => void;
}

export class MultilineEditor {
  private element: blessed.Widgets.BoxElement;
  private textBuffer: TextBuffer;
  private options: MultilineEditorOptions;
  private isActive = false;
  private placeholder: string;
  private maxLines: number;
  private showLineNumbers: boolean;
  private lineNumberWidth = 0;

  constructor(options: MultilineEditorOptions) {
    this.options = options;
    this.placeholder = options.placeholder || 'Type your message...';
    this.maxLines = options.maxLines || 50;
    this.showLineNumbers = options.showLineNumbers || false;

    // Create text buffer
    this.textBuffer = new TextBuffer({
      tabSize: options.tabSize || 2,
      wrapMode: options.wordWrap ? 'word' : 'none',
    });

    // Create blessed element
    this.element = blessed.box({
      parent: options.parent,
      top: options.top || 0,
      left: options.left || 0,
      width: options.width || '100%',
      height: options.height || 10,
      border: options.border ? { type: 'line' } : undefined,
      style: {
        fg: 'white',
        bg: 'black',
        focus: {
          border: { fg: 'cyan' },
        },
        ...options.style,
      },
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: false,
      tags: false,
    });

    this.setupEventHandlers();
    this.updateDisplay();
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Key handling
    this.element.key(['enter'], () => {
      if (this.isActive) {
        this.handleEnter();
      }
    });

    this.element.key(['C-c'], () => {
      if (this.isActive && this.options.onCancel) {
        this.options.onCancel();
      }
    });

    this.element.key(['C-d'], () => {
      if (this.isActive && this.options.onSubmit) {
        this.options.onSubmit(this.getText());
      }
    });

    this.element.key(['backspace'], () => {
      if (this.isActive) {
        this.textBuffer.deleteChar();
        this.updateDisplay();
        this.notifyChange();
      }
    });

    this.element.key(['delete'], () => {
      if (this.isActive) {
        this.textBuffer.deleteCharForward();
        this.updateDisplay();
        this.notifyChange();
      }
    });

    // Cursor movement
    this.element.key(['up'], () => {
      if (this.isActive) {
        this.textBuffer.moveCursorBy(-1, 0);
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    this.element.key(['down'], () => {
      if (this.isActive) {
        this.textBuffer.moveCursorBy(1, 0);
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    this.element.key(['left'], () => {
      if (this.isActive) {
        this.textBuffer.moveCursorBy(0, -1);
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    this.element.key(['right'], () => {
      if (this.isActive) {
        this.textBuffer.moveCursorBy(0, 1);
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    // Word movement
    this.element.key(['C-left'], () => {
      if (this.isActive) {
        this.textBuffer.moveWordBackward();
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    this.element.key(['C-right'], () => {
      if (this.isActive) {
        this.textBuffer.moveWordForward();
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    // Line movement
    this.element.key(['home'], () => {
      if (this.isActive) {
        this.textBuffer.moveToLineStart();
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    this.element.key(['end'], () => {
      if (this.isActive) {
        this.textBuffer.moveToLineEnd();
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    // Buffer movement
    this.element.key(['C-home'], () => {
      if (this.isActive) {
        this.textBuffer.moveToBufferStart();
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    this.element.key(['C-end'], () => {
      if (this.isActive) {
        this.textBuffer.moveToBufferEnd();
        this.updateDisplay();
        this.notifyCursorMove();
      }
    });

    // Undo/Redo
    this.element.key(['C-z'], () => {
      if (this.isActive) {
        this.textBuffer.undo();
        this.updateDisplay();
        this.notifyChange();
      }
    });

    this.element.key(['C-y'], () => {
      if (this.isActive) {
        this.textBuffer.redo();
        this.updateDisplay();
        this.notifyChange();
      }
    });

    // Delete operations
    this.element.key(['C-k'], () => {
      if (this.isActive) {
        this.textBuffer.deleteToLineEnd();
        this.updateDisplay();
        this.notifyChange();
      }
    });

    this.element.key(['C-u'], () => {
      if (this.isActive) {
        this.textBuffer.deleteLine();
        this.updateDisplay();
        this.notifyChange();
      }
    });

    // Character input
    this.element.on('keypress', (ch: string, key: any) => {
      if (!this.isActive || !ch || key.name === 'enter' || key.ctrl || key.meta) {
        return;
      }

      this.textBuffer.insertText(ch);
      this.updateDisplay();
      this.notifyChange();
    });

    // Mouse support
    this.element.on('click', (data: any) => {
      if (this.isActive && data) {
        const { x, y } = data;
        this.handleMouseClick(x, y);
      }
    });

    // Focus handling
    this.element.on('focus', () => {
      this.isActive = true;
      this.updateDisplay();
    });

    this.element.on('blur', () => {
      this.isActive = false;
      this.updateDisplay();
    });
  }

  /**
   * Handle enter key press
   */
  private handleEnter(): void {
    // Check if we should submit (Ctrl+Enter or single line mode)
    if (this.maxLines === 1) {
      if (this.options.onSubmit) {
        this.options.onSubmit(this.getText());
      }
      return;
    }

    // Insert new line
    this.textBuffer.insertNewLine();
    this.updateDisplay();
    this.notifyChange();
  }

  /**
   * Handle mouse click
   */
  private handleMouseClick(x: number, y: number): void {
    const scrollOffset = this.textBuffer.getScrollPosition();
    const lineNumberOffset = this.showLineNumbers ? this.lineNumberWidth : 0;
    
    const row = y + scrollOffset.row;
    const col = Math.max(0, x - lineNumberOffset + scrollOffset.col);
    
    this.textBuffer.moveCursor(row, col);
    this.updateDisplay();
    this.notifyCursorMove();
  }

  /**
   * Update visual display
   */
  private updateDisplay(): void {
    try {
      const { columns, rows } = useTerminalSize();
      const elementWidth = (this.element.width as number) || columns;
      const elementHeight = (this.element.height as number) || rows;
      
      // Update text buffer viewport
      const contentWidth = elementWidth - (this.options.border ? 2 : 0);
      const contentHeight = elementHeight - (this.options.border ? 2 : 0);
      
      this.textBuffer.setViewport(contentWidth, contentHeight);
      
      // Calculate line number width if needed
      if (this.showLineNumbers) {
        const lineCount = this.textBuffer.getLineCount();
        this.lineNumberWidth = Math.max(3, lineCount.toString().length + 1);
      }
      
      // Get visible lines
      const visibleLines = this.textBuffer.getVisibleLines();
      const scrollPos = this.textBuffer.getScrollPosition();
      const cursorPos = this.textBuffer.getCursorPosition();
      
      // Build display content
      const displayLines: string[] = [];
      
      for (let i = 0; i < visibleLines.length; i++) {
        const lineNumber = scrollPos.row + i + 1;
        let line = visibleLines[i] || '';
        
        // Add line numbers if enabled
        if (this.showLineNumbers) {
          const lineNumStr = lineNumber.toString().padStart(this.lineNumberWidth - 1, ' ');
          line = `${lineNumStr} ${line}`;
        }
        
        // Add cursor if on this line
        if (this.isActive && lineNumber - 1 === cursorPos.row) {
          const cursorCol = cursorPos.col + (this.showLineNumbers ? this.lineNumberWidth : 0);
          if (cursorCol >= 0 && cursorCol <= line.length) {
            line = line.slice(0, cursorCol) + '█' + line.slice(cursorCol);
          }
        }
        
        displayLines.push(line);
      }
      
      // Show placeholder if empty
      if (displayLines.length === 0 || (displayLines.length === 1 && displayLines[0].trim() === '')) {
        if (!this.isActive && this.placeholder) {
          displayLines[0] = this.placeholder;
        }
      }
      
      this.element.setContent(displayLines.join('\n'));
      
      if (this.element.screen) {
        this.element.screen.render();
      }
      
    } catch (error) {
      logError('Failed to update multiline editor display', error as Error);
    }
  }

  /**
   * Notify change event
   */
  private notifyChange(): void {
    if (this.options.onChange) {
      this.options.onChange(this.getText());
    }
  }

  /**
   * Notify cursor move event
   */
  private notifyCursorMove(): void {
    if (this.options.onCursorMove) {
      const pos = this.textBuffer.getCursorPosition();
      this.options.onCursorMove(pos.row, pos.col);
    }
  }

  /**
   * Get current text content
   */
  getText(): string {
    return this.textBuffer.getText();
  }

  /**
   * Set text content
   */
  setText(text: string): void {
    this.textBuffer.setText(text);
    this.updateDisplay();
    this.notifyChange();
  }

  /**
   * Clear all content
   */
  clear(): void {
    this.textBuffer.clear();
    this.updateDisplay();
    this.notifyChange();
  }

  /**
   * Focus the editor
   */
  focus(): void {
    this.element.focus();
  }

  /**
   * Check if editor is empty
   */
  isEmpty(): boolean {
    return this.textBuffer.isEmpty();
  }

  /**
   * Get cursor position
   */
  getCursorPosition(): { row: number; col: number } {
    return this.textBuffer.getCursorPosition();
  }

  /**
   * Get line count
   */
  getLineCount(): number {
    return this.textBuffer.getLineCount();
  }

  /**
   * Get the underlying blessed element
   */
  getElement(): blessed.Widgets.BoxElement {
    return this.element;
  }

  /**
   * Destroy the editor
   */
  destroy(): void {
    this.element.destroy();
  }
}

export default MultilineEditor;
