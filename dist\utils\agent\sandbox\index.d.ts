/**
 * Multi-Platform Sandbox System
 *
 * Provides secure command execution with platform-specific isolation,
 * resource limits, and safety controls.
 */
import type { ExecInput, ExecResult } from '../../../types/index.js';
export interface SandboxOptions {
    workingDirectory?: string;
    timeout?: number;
    maxMemory?: number;
    maxCpuTime?: number;
    allowNetworking?: boolean;
    allowFileSystem?: boolean;
    restrictedPaths?: string[];
    environmentVariables?: Record<string, string>;
    uid?: number;
    gid?: number;
}
export interface SandboxCapabilities {
    isolation: boolean;
    resourceLimits: boolean;
    networkRestriction: boolean;
    fileSystemRestriction: boolean;
    userIsolation: boolean;
    processLimits: boolean;
}
export declare abstract class BaseSandbox {
    protected options: Required<SandboxOptions>;
    constructor(options?: SandboxOptions);
    /**
     * Execute command in sandbox
     */
    abstract execute(input: ExecInput): Promise<ExecResult>;
    /**
     * Get sandbox capabilities
     */
    abstract getCapabilities(): SandboxCapabilities;
    /**
     * Check if sandbox is available
     */
    abstract isAvailable(): Promise<boolean>;
    /**
     * Setup sandbox environment
     */
    abstract setup(): Promise<void>;
    /**
     * Cleanup sandbox resources
     */
    abstract cleanup(): Promise<void>;
    /**
     * Validate command before execution
     */
    protected validateCommand(input: ExecInput): {
        valid: boolean;
        errors: string[];
    };
    /**
     * Apply resource limits to command
     */
    protected applyResourceLimits(command: string[]): string[];
    /**
     * Sanitize environment variables
     */
    protected sanitizeEnvironment(): Record<string, string>;
    /**
     * Log sandbox activity
     */
    protected logActivity(action: string, details: any): void;
}
/**
 * Create platform-appropriate sandbox
 */
export declare function createSandbox(options?: SandboxOptions): BaseSandbox;
/**
 * Check if sandboxing is supported on current platform
 */
export declare function isSandboxingSupported(): Promise<boolean>;
/**
 * Get sandbox capabilities for current platform
 */
export declare function getSandboxCapabilities(): SandboxCapabilities;
/**
 * Execute command with automatic sandbox selection
 */
export declare function executeInSandbox(input: ExecInput, options?: SandboxOptions): Promise<ExecResult>;
/**
 * Test sandbox functionality
 */
export declare function testSandbox(options?: SandboxOptions): Promise<{
    supported: boolean;
    capabilities: SandboxCapabilities;
    testResults: Array<{
        test: string;
        passed: boolean;
        error?: string;
    }>;
}>;
declare const _default: {
    createSandbox: typeof createSandbox;
    executeInSandbox: typeof executeInSandbox;
    isSandboxingSupported: typeof isSandboxingSupported;
    getSandboxCapabilities: typeof getSandboxCapabilities;
    testSandbox: typeof testSandbox;
    BaseSandbox: typeof BaseSandbox;
};
export default _default;
//# sourceMappingURL=index.d.ts.map