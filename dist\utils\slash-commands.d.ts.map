{"version": 3, "file": "slash-commands.d.ts", "sourceRoot": "", "sources": ["../../src/utils/slash-commands.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,MAAM,WAAW,YAAY;IAC3B,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;IACnB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;IACtB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED;;GAEG;AACH,eAAO,MAAM,cAAc,EAAE,YAAY,EAyFxC,CAAC;AAEF;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,aAAa,UAAQ,GAAG,YAAY,EAAE,CAEtE;AAED;;GAEG;AACH,wBAAgB,0BAA0B,CAAC,aAAa,UAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,CAahG;AAED;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,MAAM,GAAG,YAAY,GAAG,IAAI,CAcnE;AAED;;GAEG;AACH,wBAAgB,0BAA0B,CAAC,KAAK,EAAE,MAAM,GAAG,YAAY,EAAE,CA6BxE;AAED;;GAEG;AACH,wBAAgB,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAG;IAChD,OAAO,EAAE,YAAY,GAAG,IAAI,CAAC;IAC7B,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,QAAQ,EAAE,MAAM,CAAC;CAClB,CAaA;AAED;;GAEG;AACH,wBAAgB,oBAAoB,CAClC,OAAO,EAAE,YAAY,EACrB,UAAU,EAAE,MAAM,EAAE,GACnB;IACD,KAAK,EAAE,OAAO,CAAC;IACf,MAAM,EAAE,MAAM,EAAE,CAAC;CAClB,CAiBA;AAED;;GAEG;AACH,wBAAgB,sBAAsB,CAAC,OAAO,EAAE,YAAY,GAAG,MAAM,CAcpE;AAED;;GAEG;AACH,wBAAgB,gBAAgB,IAAI,MAAM,CAezC;AAED;;GAEG;AACH,wBAAgB,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAErD;AAED;;GAEG;AACH,wBAAgB,eAAe,CAAC,OAAO,EAAE,YAAY,GAAG,MAAM,CAa7D"}