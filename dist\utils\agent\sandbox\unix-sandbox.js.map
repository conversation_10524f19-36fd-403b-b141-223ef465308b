{"version": 3, "file": "unix-sandbox.js", "sourceRoot": "", "sources": ["../../../../src/utils/agent/sandbox/unix-sandbox.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,KAAK,EAAgB,MAAM,eAAe,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC;AAC3C,OAAO,EAAW,IAAI,EAAE,MAAM,MAAM,CAAC;AACrC,OAAO,EAAE,WAAW,EAA4B,MAAM,YAAY,CAAC;AACnE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAGlE,MAAM,OAAO,WAAY,SAAQ,WAAW;IAClC,eAAe,GAAG,IAAI,GAAG,EAAgB,CAAC;IAC1C,cAAc,GAAkB,IAAI,CAAC;IAE7C;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,KAAgB;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,8BAA8B,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAE/D,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;YAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,OAAO;YACP,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;SAC9B,CAAC,CAAC;QAEH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,QAAQ,GAAG,KAAK,CAAC;YAErB,4BAA4B;YAC5B,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE9E,2CAA2C;YAC3C,MAAM,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBAClE,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;gBAC/B,QAAQ,EAAE,IAAI,EAAE,2BAA2B;gBAC3C,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;gBACrB,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAEhC,iBAAiB;YACjB,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;gBACpC,QAAQ,GAAG,IAAI,CAAC;gBAChB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAEzB,gBAAgB;YAChB,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACxC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,gBAAgB;YAChB,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBACxC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,sBAAsB;YACtB,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;gBAChC,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC5B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAEnC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAExC,MAAM,MAAM,GAAe;oBACzB,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,2BAA2B;oBACnE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;oBACrB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;oBACrB,QAAQ;oBACR,QAAQ;oBACR,MAAM,EAAE,MAAM,IAAI,SAAS;iBAC5B,CAAC;gBAEF,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;oBAC5B,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,QAAQ;oBACR,QAAQ;iBACT,CAAC,CAAC;gBAEH,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,wBAAwB;YACxB,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC5B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAEnC,QAAQ,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;gBAC3C,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;YAEH,iCAAiC;YACjC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe;QACb,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;QAE7C,OAAO;YACL,SAAS,EAAE,IAAI,EAAE,oCAAoC;YACrD,cAAc,EAAE,IAAI,EAAE,qBAAqB;YAC3C,kBAAkB,EAAE,OAAO,EAAE,8BAA8B;YAC3D,qBAAqB,EAAE,IAAI,EAAE,8BAA8B;YAC3D,aAAa,EAAE,IAAI,EAAE,kBAAkB;YACvC,aAAa,EAAE,IAAI,EAAE,6BAA6B;SACnD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;gBACpC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;aAC1B,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC,QAAQ,KAAK,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,QAAQ,CAAC,yBAAyB,CAAC,CAAC;QAEpC,kCAAkC;QAClC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACxF,CAAC;QAED,+CAA+C;QAC/C,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,QAAQ,CAAC,0BAA0B,CAAC,CAAC;QAErC,+BAA+B;QAC/B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAE7B,sCAAsC;QACtC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC;gBACH,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;gBAC9C,QAAQ,CAAC,WAAW,IAAI,CAAC,cAAc,GAAG,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;YACnE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAAiB,EAAE,OAAe;QAChE,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,8BAA8B;QAC9B,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC;QAEhE,mCAAmC;QACnC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;YAChE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,aAAa,aAAa,EAAE,IAAI,CAAC,CAAC;QAC/E,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;YACnE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,eAAe,aAAa,EAAE,IAAI,CAAC,CAAC;QACjF,CAAC;QAED,+CAA+C;QAC/C,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC7D,MAAM,YAAY,GAAa,EAAE,CAAC;YAElC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;gBAClC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;YAED,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAErC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,YAAY,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,YAAY,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QAE9B,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAqB;QAC5C,IAAI,CAAC;YACH,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnC,gCAAgC;gBAChC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAExB,iCAAiC;gBACjC,UAAU,CAAC,GAAG,EAAE;oBACd,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;wBACpB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC1B,CAAC;gBACH,CAAC,EAAE,IAAI,CAAC,CAAC;YACX,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,IAAI,CAAC;YACH,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;YAC9C,QAAQ,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,4DAA4D;QAC5D,OAAO,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;QAC3C,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjC,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,mBAAmB,CAAC,CAAC,CAAC;QAEtE,mCAAmC;QACnC,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACzE,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,0BAA0B;QAC1B,MAAM,iBAAiB,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;QACvF,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE,CAAC;YACvC,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC;oBACH,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;oBAC9C,QAAQ,CAAC,OAAO,MAAM,MAAM,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAC/E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,yBAAyB,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,GAAW;QACnC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,UAAU,GAAG,0CAA0C,GAAG,EAAE,CAAC;YAEnE,0BAA0B;YAC1B,SAAS,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE3C,mBAAmB;YACnB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;gBAC/B,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,uBAAuB,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9F,CAAC;YAED,wBAAwB;YACxB,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAElE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB;QAMxB,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAEzB,OAAO;YACL,WAAW,EAAE,EAAE,CAAC,QAAQ,EAAE;YAC1B,eAAe,EAAE,EAAE,CAAC,OAAO,EAAE;YAC7B,QAAQ,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;YAC1B,WAAW,EAAE,EAAE,CAAC,OAAO,EAAE;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE5C,eAAe;QACf,IAAI,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,qBAAqB;QACrB,MAAM,OAAO,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,OAAO,GAAG,SAAS,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED,eAAe,WAAW,CAAC"}