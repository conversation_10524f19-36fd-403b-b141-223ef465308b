/**
 * Sophisticated Text Buffer System
 *
 * Provides advanced multi-line text editing capabilities with Unicode support,
 * undo/redo functionality, cursor management, and viewport handling.
 */
export default class TextBuffer {
    lines = [''];
    cursorRow = 0;
    cursorCol = 0;
    scrollRow = 0;
    scrollCol = 0;
    version = 0;
    undoStack = [];
    redoStack = [];
    // Configuration
    maxUndoStates;
    tabSize;
    wrapMode;
    // Viewport dimensions
    viewportWidth = 80;
    viewportHeight = 24;
    constructor(options = {}) {
        this.maxUndoStates = options.maxUndoStates || 100;
        this.tabSize = options.tabSize || 2;
        this.wrapMode = options.wrapMode || 'none';
    }
    /**
     * Set viewport dimensions
     */
    setViewport(width, height) {
        this.viewportWidth = width;
        this.viewportHeight = height;
    }
    /**
     * Get current text content
     */
    getText() {
        return this.lines.join('\n');
    }
    /**
     * Set text content
     */
    setText(text) {
        this.saveUndoState();
        this.lines = text.split('\n');
        if (this.lines.length === 0) {
            this.lines = [''];
        }
        this.cursorRow = 0;
        this.cursorCol = 0;
        this.scrollRow = 0;
        this.scrollCol = 0;
        this.version++;
    }
    /**
     * Insert text at cursor position
     */
    insertText(text) {
        this.saveUndoState();
        const lines = text.split('\n');
        const currentLine = this.lines[this.cursorRow];
        if (lines.length === 1) {
            // Single line insertion
            const newLine = currentLine.slice(0, this.cursorCol) + text + currentLine.slice(this.cursorCol);
            this.lines[this.cursorRow] = newLine;
            this.cursorCol += text.length;
        }
        else {
            // Multi-line insertion
            const beforeCursor = currentLine.slice(0, this.cursorCol);
            const afterCursor = currentLine.slice(this.cursorCol);
            // First line
            this.lines[this.cursorRow] = beforeCursor + lines[0];
            // Middle lines
            for (let i = 1; i < lines.length - 1; i++) {
                this.lines.splice(this.cursorRow + i, 0, lines[i]);
            }
            // Last line
            const lastLine = lines[lines.length - 1] + afterCursor;
            this.lines.splice(this.cursorRow + lines.length - 1, 0, lastLine);
            // Update cursor position
            this.cursorRow += lines.length - 1;
            this.cursorCol = lines[lines.length - 1].length;
        }
        this.version++;
        this.ensureCursorVisible();
    }
    /**
     * Delete character at cursor position
     */
    deleteChar() {
        if (this.cursorCol === 0 && this.cursorRow === 0) {
            return; // Nothing to delete
        }
        this.saveUndoState();
        if (this.cursorCol > 0) {
            // Delete character in current line
            const line = this.lines[this.cursorRow];
            this.lines[this.cursorRow] = line.slice(0, this.cursorCol - 1) + line.slice(this.cursorCol);
            this.cursorCol--;
        }
        else {
            // Delete newline - merge with previous line
            const currentLine = this.lines[this.cursorRow];
            const previousLine = this.lines[this.cursorRow - 1];
            this.lines[this.cursorRow - 1] = previousLine + currentLine;
            this.lines.splice(this.cursorRow, 1);
            this.cursorRow--;
            this.cursorCol = previousLine.length;
        }
        this.version++;
        this.ensureCursorVisible();
    }
    /**
     * Delete character after cursor position
     */
    deleteCharForward() {
        const line = this.lines[this.cursorRow];
        if (this.cursorCol < line.length) {
            // Delete character in current line
            this.saveUndoState();
            this.lines[this.cursorRow] = line.slice(0, this.cursorCol) + line.slice(this.cursorCol + 1);
            this.version++;
        }
        else if (this.cursorRow < this.lines.length - 1) {
            // Delete newline - merge with next line
            this.saveUndoState();
            const nextLine = this.lines[this.cursorRow + 1];
            this.lines[this.cursorRow] = line + nextLine;
            this.lines.splice(this.cursorRow + 1, 1);
            this.version++;
        }
    }
    /**
     * Insert new line at cursor position
     */
    insertNewLine() {
        this.saveUndoState();
        const line = this.lines[this.cursorRow];
        const beforeCursor = line.slice(0, this.cursorCol);
        const afterCursor = line.slice(this.cursorCol);
        this.lines[this.cursorRow] = beforeCursor;
        this.lines.splice(this.cursorRow + 1, 0, afterCursor);
        this.cursorRow++;
        this.cursorCol = 0;
        this.version++;
        this.ensureCursorVisible();
    }
    /**
     * Move cursor to position
     */
    moveCursor(row, col) {
        this.cursorRow = Math.max(0, Math.min(row, this.lines.length - 1));
        const line = this.lines[this.cursorRow];
        this.cursorCol = Math.max(0, Math.min(col, line.length));
        this.ensureCursorVisible();
    }
    /**
     * Move cursor by offset
     */
    moveCursorBy(deltaRow, deltaCol) {
        this.moveCursor(this.cursorRow + deltaRow, this.cursorCol + deltaCol);
    }
    /**
     * Move cursor to beginning of line
     */
    moveToLineStart() {
        this.cursorCol = 0;
        this.ensureCursorVisible();
    }
    /**
     * Move cursor to end of line
     */
    moveToLineEnd() {
        this.cursorCol = this.lines[this.cursorRow].length;
        this.ensureCursorVisible();
    }
    /**
     * Move cursor to beginning of buffer
     */
    moveToBufferStart() {
        this.cursorRow = 0;
        this.cursorCol = 0;
        this.ensureCursorVisible();
    }
    /**
     * Move cursor to end of buffer
     */
    moveToBufferEnd() {
        this.cursorRow = this.lines.length - 1;
        this.cursorCol = this.lines[this.cursorRow].length;
        this.ensureCursorVisible();
    }
    /**
     * Move cursor by word
     */
    moveWordForward() {
        const line = this.lines[this.cursorRow];
        let col = this.cursorCol;
        // Skip current word
        while (col < line.length && /\w/.test(line[col])) {
            col++;
        }
        // Skip whitespace
        while (col < line.length && /\s/.test(line[col])) {
            col++;
        }
        if (col >= line.length && this.cursorRow < this.lines.length - 1) {
            // Move to next line
            this.cursorRow++;
            this.cursorCol = 0;
        }
        else {
            this.cursorCol = col;
        }
        this.ensureCursorVisible();
    }
    /**
     * Move cursor backward by word
     */
    moveWordBackward() {
        let col = this.cursorCol;
        const line = this.lines[this.cursorRow];
        if (col === 0 && this.cursorRow > 0) {
            // Move to previous line
            this.cursorRow--;
            this.cursorCol = this.lines[this.cursorRow].length;
            return;
        }
        // Skip whitespace
        while (col > 0 && /\s/.test(line[col - 1])) {
            col--;
        }
        // Skip word
        while (col > 0 && /\w/.test(line[col - 1])) {
            col--;
        }
        this.cursorCol = col;
        this.ensureCursorVisible();
    }
    /**
     * Get cursor position
     */
    getCursorPosition() {
        return { row: this.cursorRow, col: this.cursorCol };
    }
    /**
     * Get scroll position
     */
    getScrollPosition() {
        return { row: this.scrollRow, col: this.scrollCol };
    }
    /**
     * Ensure cursor is visible in viewport
     */
    ensureCursorVisible() {
        // Vertical scrolling
        if (this.cursorRow < this.scrollRow) {
            this.scrollRow = this.cursorRow;
        }
        else if (this.cursorRow >= this.scrollRow + this.viewportHeight) {
            this.scrollRow = this.cursorRow - this.viewportHeight + 1;
        }
        // Horizontal scrolling
        if (this.cursorCol < this.scrollCol) {
            this.scrollCol = this.cursorCol;
        }
        else if (this.cursorCol >= this.scrollCol + this.viewportWidth) {
            this.scrollCol = this.cursorCol - this.viewportWidth + 1;
        }
    }
    /**
     * Save current state for undo
     */
    saveUndoState() {
        const state = {
            lines: [...this.lines],
            cursorRow: this.cursorRow,
            cursorCol: this.cursorCol,
            scrollRow: this.scrollRow,
            scrollCol: this.scrollCol,
            version: this.version,
        };
        this.undoStack.push(state);
        // Limit undo stack size
        if (this.undoStack.length > this.maxUndoStates) {
            this.undoStack.shift();
        }
        // Clear redo stack
        this.redoStack = [];
    }
    /**
     * Undo last operation
     */
    undo() {
        if (this.undoStack.length === 0) {
            return false;
        }
        // Save current state to redo stack
        const currentState = {
            lines: [...this.lines],
            cursorRow: this.cursorRow,
            cursorCol: this.cursorCol,
            scrollRow: this.scrollRow,
            scrollCol: this.scrollCol,
            version: this.version,
        };
        this.redoStack.push(currentState);
        // Restore previous state
        const state = this.undoStack.pop();
        this.lines = [...state.lines];
        this.cursorRow = state.cursorRow;
        this.cursorCol = state.cursorCol;
        this.scrollRow = state.scrollRow;
        this.scrollCol = state.scrollCol;
        this.version = state.version;
        return true;
    }
    /**
     * Redo last undone operation
     */
    redo() {
        if (this.redoStack.length === 0) {
            return false;
        }
        // Save current state to undo stack
        this.saveUndoState();
        // Restore redo state
        const state = this.redoStack.pop();
        this.lines = [...state.lines];
        this.cursorRow = state.cursorRow;
        this.cursorCol = state.cursorCol;
        this.scrollRow = state.scrollRow;
        this.scrollCol = state.scrollCol;
        this.version = state.version;
        return true;
    }
    /**
     * Get visible lines for rendering
     */
    getVisibleLines() {
        const endRow = Math.min(this.scrollRow + this.viewportHeight, this.lines.length);
        return this.lines.slice(this.scrollRow, endRow).map(line => {
            if (this.scrollCol >= line.length) {
                return '';
            }
            return line.slice(this.scrollCol, this.scrollCol + this.viewportWidth);
        });
    }
    /**
     * Get line count
     */
    getLineCount() {
        return this.lines.length;
    }
    /**
     * Get line at index
     */
    getLine(index) {
        return this.lines[index] || '';
    }
    /**
     * Delete current line
     */
    deleteLine() {
        if (this.lines.length === 1) {
            this.saveUndoState();
            this.lines[0] = '';
            this.cursorCol = 0;
            this.version++;
            return;
        }
        this.saveUndoState();
        this.lines.splice(this.cursorRow, 1);
        if (this.cursorRow >= this.lines.length) {
            this.cursorRow = this.lines.length - 1;
        }
        this.cursorCol = Math.min(this.cursorCol, this.lines[this.cursorRow].length);
        this.version++;
        this.ensureCursorVisible();
    }
    /**
     * Delete from cursor to end of line
     */
    deleteToLineEnd() {
        this.saveUndoState();
        const line = this.lines[this.cursorRow];
        this.lines[this.cursorRow] = line.slice(0, this.cursorCol);
        this.version++;
    }
    /**
     * Delete word at cursor
     */
    deleteWord() {
        const line = this.lines[this.cursorRow];
        let endCol = this.cursorCol;
        // Find end of current word
        while (endCol < line.length && /\w/.test(line[endCol])) {
            endCol++;
        }
        if (endCol > this.cursorCol) {
            this.saveUndoState();
            this.lines[this.cursorRow] = line.slice(0, this.cursorCol) + line.slice(endCol);
            this.version++;
        }
    }
    /**
     * Get buffer statistics
     */
    getStats() {
        const text = this.getText();
        const words = text.split(/\s+/).filter(word => word.length > 0).length;
        return {
            lines: this.lines.length,
            characters: text.length,
            words,
            version: this.version,
        };
    }
    /**
     * Clear all content
     */
    clear() {
        this.saveUndoState();
        this.lines = [''];
        this.cursorRow = 0;
        this.cursorCol = 0;
        this.scrollRow = 0;
        this.scrollCol = 0;
        this.version++;
    }
    /**
     * Check if buffer is empty
     */
    isEmpty() {
        return this.lines.length === 1 && this.lines[0] === '';
    }
    /**
     * Get current version
     */
    getVersion() {
        return this.version;
    }
}
//# sourceMappingURL=text-buffer.js.map