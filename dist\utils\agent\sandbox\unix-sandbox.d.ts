/**
 * Unix Sandbox Implementation
 *
 * Provides Unix/Linux/macOS sandboxing using chroot, namespaces,
 * resource limits, and security restrictions.
 */
import { BaseSandbox, type SandboxCapabilities } from './index.js';
import type { ExecInput, ExecResult } from '../../../types/index.js';
export declare class UnixSandbox extends BaseSandbox {
    private activeProcesses;
    private tempSandboxDir;
    /**
     * Execute command in Unix sandbox
     */
    execute(input: ExecInput): Promise<ExecResult>;
    /**
     * Get Unix sandbox capabilities
     */
    getCapabilities(): SandboxCapabilities;
    /**
     * Check if Unix sandbox is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Setup Unix sandbox environment
     */
    setup(): Promise<void>;
    /**
     * Cleanup Unix sandbox resources
     */
    cleanup(): Promise<void>;
    /**
     * Prepare sandboxed command with security restrictions
     */
    private prepareSandboxedCommand;
    /**
     * Kill process group to ensure all child processes are terminated
     */
    private killProcessGroup;
    /**
     * Check if unshare command is available
     */
    private hasUnshareSupport;
    /**
     * Check if chroot should be used
     */
    private shouldUseChroot;
    /**
     * Create sandbox directory for chroot
     */
    private createSandboxDirectory;
    /**
     * Apply cgroup limits (Linux only)
     */
    private applyCgroupLimits;
    /**
     * Get system resource information
     */
    private getSystemResources;
    /**
     * Check if system has sufficient resources
     */
    private hasInsufficientResources;
}
export default UnixSandbox;
//# sourceMappingURL=unix-sandbox.d.ts.map