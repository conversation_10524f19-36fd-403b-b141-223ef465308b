/**
 * Unix Sandbox Implementation
 *
 * Provides Unix/Linux/macOS sandboxing using chroot, namespaces,
 * resource limits, and security restrictions.
 */
import { spawn } from 'child_process';
import { existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { BaseSandbox } from './index.js';
import { logDebug, logError, logWarn } from '../../logger/log.js';
export class UnixSandbox extends BaseSandbox {
    activeProcesses = new Set();
    tempSandboxDir = null;
    /**
     * Execute command in Unix sandbox
     */
    async execute(input) {
        const validation = this.validateCommand(input);
        if (!validation.valid) {
            throw new Error(`Command validation failed: ${validation.errors.join(', ')}`);
        }
        const startTime = Date.now();
        const workdir = input.workdir || this.options.workingDirectory;
        this.logActivity('execute', {
            command: input.command,
            workdir,
            timeout: this.options.timeout,
        });
        return new Promise((resolve, reject) => {
            let stdout = '';
            let stderr = '';
            let timedOut = false;
            // Prepare sandboxed command
            const sandboxedCommand = this.prepareSandboxedCommand(input.command, workdir);
            // Spawn process with Unix-specific options
            const child = spawn(sandboxedCommand[0], sandboxedCommand.slice(1), {
                cwd: workdir,
                env: this.sanitizeEnvironment(),
                stdio: ['pipe', 'pipe', 'pipe'],
                detached: true, // Create new process group
                uid: this.options.uid,
                gid: this.options.gid,
            });
            this.activeProcesses.add(child);
            // Set up timeout
            const timeoutHandle = setTimeout(() => {
                timedOut = true;
                this.killProcessGroup(child);
            }, this.options.timeout);
            // Handle stdout
            child.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            // Handle stderr
            child.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            // Handle process exit
            child.on('exit', (code, signal) => {
                clearTimeout(timeoutHandle);
                this.activeProcesses.delete(child);
                const duration = Date.now() - startTime;
                const result = {
                    exitCode: timedOut ? 124 : (code || 0), // 124 is timeout exit code
                    stdout: stdout.trim(),
                    stderr: stderr.trim(),
                    duration,
                    timedOut,
                    signal: signal || undefined,
                };
                this.logActivity('completed', {
                    exitCode: result.exitCode,
                    duration,
                    timedOut,
                });
                resolve(result);
            });
            // Handle process errors
            child.on('error', (error) => {
                clearTimeout(timeoutHandle);
                this.activeProcesses.delete(child);
                logError('Process execution error', error);
                reject(new Error(`Process execution failed: ${error.message}`));
            });
            // Close stdin to prevent hanging
            child.stdin?.end();
        });
    }
    /**
     * Get Unix sandbox capabilities
     */
    getCapabilities() {
        const isLinux = process.platform === 'linux';
        return {
            isolation: true, // Process isolation with namespaces
            resourceLimits: true, // ulimit and cgroups
            networkRestriction: isLinux, // Network namespaces on Linux
            fileSystemRestriction: true, // chroot and mount namespaces
            userIsolation: true, // User namespaces
            processLimits: true, // Process limits and control
        };
    }
    /**
     * Check if Unix sandbox is available
     */
    async isAvailable() {
        const unixPlatforms = ['linux', 'darwin', 'freebsd', 'openbsd'];
        if (!unixPlatforms.includes(process.platform)) {
            return false;
        }
        try {
            // Test basic command execution
            const testResult = await this.execute({
                command: ['echo', 'test'],
            });
            return testResult.exitCode === 0;
        }
        catch (error) {
            logError('Unix sandbox availability check failed', error);
            return false;
        }
    }
    /**
     * Setup Unix sandbox environment
     */
    async setup() {
        logDebug('Setting up Unix sandbox');
        // Verify working directory exists
        if (!existsSync(this.options.workingDirectory)) {
            throw new Error(`Working directory does not exist: ${this.options.workingDirectory}`);
        }
        // Create temporary sandbox directory if needed
        if (this.shouldUseChroot()) {
            this.tempSandboxDir = await this.createSandboxDirectory();
        }
    }
    /**
     * Cleanup Unix sandbox resources
     */
    async cleanup() {
        logDebug('Cleaning up Unix sandbox');
        // Kill any remaining processes
        for (const process of this.activeProcesses) {
            this.killProcessGroup(process);
        }
        this.activeProcesses.clear();
        // Cleanup temporary sandbox directory
        if (this.tempSandboxDir) {
            try {
                const { execSync } = require('child_process');
                execSync(`rm -rf "${this.tempSandboxDir}"`, { stdio: 'ignore' });
            }
            catch (error) {
                logWarn('Failed to cleanup sandbox directory', error);
            }
            this.tempSandboxDir = null;
        }
    }
    /**
     * Prepare sandboxed command with security restrictions
     */
    prepareSandboxedCommand(command, workdir) {
        const sandboxedCmd = [];
        // Use timeout for time limits
        sandboxedCmd.push('timeout', `${this.options.timeout / 1000}s`);
        // Add resource limits using ulimit
        if (this.options.maxMemory > 0) {
            const memoryLimitKB = Math.floor(this.options.maxMemory / 1024);
            sandboxedCmd.push('sh', '-c', `ulimit -v ${memoryLimitKB}; exec "$@"`, '--');
        }
        // Add CPU time limit
        if (this.options.maxCpuTime > 0) {
            const cpuLimitSeconds = Math.floor(this.options.maxCpuTime / 1000);
            sandboxedCmd.push('sh', '-c', `ulimit -t ${cpuLimitSeconds}; exec "$@"`, '--');
        }
        // Use unshare for namespace isolation on Linux
        if (process.platform === 'linux' && this.hasUnshareSupport()) {
            const unshareFlags = [];
            if (!this.options.allowNetworking) {
                unshareFlags.push('--net');
            }
            unshareFlags.push('--pid', '--fork');
            if (unshareFlags.length > 0) {
                sandboxedCmd.push('unshare', ...unshareFlags);
            }
        }
        // Add the actual command
        sandboxedCmd.push(...command);
        return sandboxedCmd;
    }
    /**
     * Kill process group to ensure all child processes are terminated
     */
    killProcessGroup(process) {
        try {
            if (process.pid && !process.killed) {
                // Kill the entire process group
                process.kill('SIGTERM');
                // Force kill after a short delay
                setTimeout(() => {
                    if (!process.killed) {
                        process.kill('SIGKILL');
                    }
                }, 1000);
            }
        }
        catch (error) {
            logWarn('Failed to kill process group', error);
        }
    }
    /**
     * Check if unshare command is available
     */
    hasUnshareSupport() {
        try {
            const { execSync } = require('child_process');
            execSync('which unshare', { stdio: 'ignore' });
            return true;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Check if chroot should be used
     */
    shouldUseChroot() {
        // Only use chroot if running as root and explicitly enabled
        return process.getuid?.() === 0 && this.options.restrictedPaths.length > 0;
    }
    /**
     * Create sandbox directory for chroot
     */
    async createSandboxDirectory() {
        const { mkdtemp } = require('fs').promises;
        const { tmpdir } = require('os');
        const sandboxDir = await mkdtemp(join(tmpdir(), 'kritrima-sandbox-'));
        // Create basic directory structure
        const dirs = ['bin', 'lib', 'lib64', 'usr/bin', 'usr/lib', 'tmp', 'dev'];
        for (const dir of dirs) {
            mkdirSync(join(sandboxDir, dir), { recursive: true });
        }
        // Copy essential binaries
        const essentialBinaries = ['/bin/sh', '/bin/bash', '/bin/ls', '/bin/cat', '/bin/echo'];
        for (const binary of essentialBinaries) {
            if (existsSync(binary)) {
                try {
                    const { execSync } = require('child_process');
                    execSync(`cp "${binary}" "${join(sandboxDir, 'bin')}"`, { stdio: 'ignore' });
                }
                catch (error) {
                    logWarn(`Failed to copy binary ${binary}`, error);
                }
            }
        }
        return sandboxDir;
    }
    /**
     * Apply cgroup limits (Linux only)
     */
    applyCgroupLimits(pid) {
        if (process.platform !== 'linux') {
            return;
        }
        try {
            const { writeFileSync } = require('fs');
            const cgroupPath = `/sys/fs/cgroup/memory/kritrima-sandbox-${pid}`;
            // Create cgroup directory
            mkdirSync(cgroupPath, { recursive: true });
            // Set memory limit
            if (this.options.maxMemory > 0) {
                writeFileSync(join(cgroupPath, 'memory.limit_in_bytes'), this.options.maxMemory.toString());
            }
            // Add process to cgroup
            writeFileSync(join(cgroupPath, 'cgroup.procs'), pid.toString());
        }
        catch (error) {
            logWarn('Failed to apply cgroup limits', error);
        }
    }
    /**
     * Get system resource information
     */
    getSystemResources() {
        const os = require('os');
        return {
            totalMemory: os.totalmem(),
            availableMemory: os.freemem(),
            cpuCount: os.cpus().length,
            loadAverage: os.loadavg(),
        };
    }
    /**
     * Check if system has sufficient resources
     */
    hasInsufficientResources() {
        const resources = this.getSystemResources();
        // Check memory
        if (resources.availableMemory < this.options.maxMemory * 2) {
            return true;
        }
        // Check load average
        const avgLoad = resources.loadAverage[0];
        if (avgLoad > resources.cpuCount * 2) {
            return true;
        }
        return false;
    }
}
export default UnixSandbox;
//# sourceMappingURL=unix-sandbox.js.map