/**
 * Confirmation Hook
 *
 * Manages confirmation queue and provides promise-based confirmation API
 * for command approval and user interactions.
 */
import { EventEmitter } from 'events';
import blessed from 'blessed';
export interface ConfirmationResult {
    approved: boolean;
    reason?: string;
    always?: boolean;
}
export interface ConfirmationRequest {
    id: string;
    prompt: blessed.Widgets.Element;
    explanation?: string;
    resolve: (result: ConfirmationResult) => void;
    reject: (error: Error) => void;
}
declare class ConfirmationManager extends EventEmitter {
    private queue;
    private currentRequest;
    private isProcessing;
    /**
     * Request confirmation from user
     */
    requestConfirmation(prompt: blessed.Widgets.Element, explanation?: string): Promise<ConfirmationResult>;
    /**
     * Submit confirmation result
     */
    submitConfirmation(result: ConfirmationResult): void;
    /**
     * Cancel current confirmation
     */
    cancelConfirmation(reason?: string): void;
    /**
     * Get current confirmation request
     */
    getCurrentRequest(): ConfirmationRequest | null;
    /**
     * Check if confirmation is pending
     */
    isPending(): boolean;
    /**
     * Clear all pending confirmations
     */
    clearQueue(): void;
    /**
     * Process confirmation queue
     */
    private processQueue;
}
declare const confirmationManager: ConfirmationManager;
/**
 * Use confirmation hook
 */
export declare function useConfirmation(): {
    submitConfirmation: (result: ConfirmationResult) => void;
    requestConfirmation: (prompt: blessed.Widgets.Element, explanation?: string) => Promise<ConfirmationResult>;
    confirmationPrompt: blessed.Widgets.Element | null;
    explanation?: string;
    isPending: boolean;
};
/**
 * Create confirmation prompt element
 */
export declare function createConfirmationPrompt(screen: blessed.Widgets.Screen, message: string, options?: {
    title?: string;
    width?: string | number;
    height?: string | number;
    border?: boolean;
}): blessed.Widgets.BoxElement;
/**
 * Create yes/no confirmation dialog
 */
export declare function createYesNoDialog(screen: blessed.Widgets.Screen, message: string, title?: string): Promise<boolean>;
/**
 * Create input dialog
 */
export declare function createInputDialog(screen: blessed.Widgets.Screen, message: string, title?: string, defaultValue?: string): Promise<string | null>;
export default confirmationManager;
//# sourceMappingURL=use-confirmation.d.ts.map