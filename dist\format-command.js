/**
 * Command Formatting Utilities
 *
 * Provides intelligent command display formatting with shell quoting,
 * wrapper detection, and user-friendly presentation.
 */
/**
 * Format command array for display
 */
export function formatCommandForDisplay(command) {
    if (!command || command.length === 0) {
        return '';
    }
    // Handle bash -lc wrapper unwrapping
    const unwrapped = unwrapBashCommand(command);
    // Quote arguments that need quoting
    const quoted = unwrapped.map(arg => quoteArgumentIfNeeded(arg));
    return quoted.join(' ');
}
/**
 * Unwrap bash -lc command wrappers
 */
function unwrapBashCommand(command) {
    // Check for bash -lc wrapper pattern
    if (command.length >= 3 &&
        (command[0] === 'bash' || command[0] === '/bin/bash') &&
        command[1] === '-lc') {
        // Extract the actual command from the -lc argument
        const wrappedCommand = command[2];
        // Try to parse the wrapped command
        try {
            const parsed = parseShellCommand(wrappedCommand);
            return parsed;
        }
        catch (error) {
            // If parsing fails, return the wrapped command as-is
            return [wrappedCommand];
        }
    }
    // Check for other shell wrappers
    if (command.length >= 3 &&
        (command[0] === 'sh' || command[0] === '/bin/sh') &&
        command[1] === '-c') {
        const wrappedCommand = command[2];
        try {
            const parsed = parseShellCommand(wrappedCommand);
            return parsed;
        }
        catch (error) {
            return [wrappedCommand];
        }
    }
    return command;
}
/**
 * Parse shell command string into arguments
 */
function parseShellCommand(commandString) {
    const args = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    let escaped = false;
    for (let i = 0; i < commandString.length; i++) {
        const char = commandString[i];
        if (escaped) {
            current += char;
            escaped = false;
            continue;
        }
        if (char === '\\') {
            escaped = true;
            continue;
        }
        if (inQuotes) {
            if (char === quoteChar) {
                inQuotes = false;
                quoteChar = '';
            }
            else {
                current += char;
            }
        }
        else {
            if (char === '"' || char === "'") {
                inQuotes = true;
                quoteChar = char;
            }
            else if (char === ' ' || char === '\t') {
                if (current) {
                    args.push(current);
                    current = '';
                }
            }
            else {
                current += char;
            }
        }
    }
    if (current) {
        args.push(current);
    }
    return args;
}
/**
 * Quote argument if it contains special characters
 */
function quoteArgumentIfNeeded(arg) {
    // Don't quote if already quoted
    if ((arg.startsWith('"') && arg.endsWith('"')) ||
        (arg.startsWith("'") && arg.endsWith("'"))) {
        return arg;
    }
    // Check if quoting is needed
    const needsQuoting = /[\s<>|&;(){}[\]$`"'\\*?~]/.test(arg);
    if (!needsQuoting) {
        return arg;
    }
    // Use single quotes for simplicity, escape any single quotes in the string
    const escaped = arg.replace(/'/g, "'\"'\"'");
    return `'${escaped}'`;
}
/**
 * Format command with working directory context
 */
export function formatCommandWithContext(command, workdir, currentDir) {
    const formattedCommand = formatCommandForDisplay(command);
    if (!workdir || !currentDir || workdir === currentDir) {
        return formattedCommand;
    }
    // Show working directory if different from current
    const relativeDir = getRelativePath(workdir, currentDir);
    return `cd ${quoteArgumentIfNeeded(relativeDir)} && ${formattedCommand}`;
}
/**
 * Get relative path for display
 */
function getRelativePath(target, base) {
    const path = require('path');
    try {
        const relative = path.relative(base, target);
        // If relative path is shorter, use it
        if (relative.length < target.length && !relative.startsWith('..')) {
            return relative || '.';
        }
        return target;
    }
    catch (error) {
        return target;
    }
}
/**
 * Format command for logging
 */
export function formatCommandForLogging(command, workdir, env) {
    const parts = [];
    // Add working directory
    if (workdir) {
        parts.push(`[${workdir}]`);
    }
    // Add environment variables (only non-standard ones)
    if (env) {
        const envVars = Object.entries(env)
            .filter(([key]) => !isStandardEnvVar(key))
            .map(([key, value]) => `${key}=${value}`)
            .join(' ');
        if (envVars) {
            parts.push(envVars);
        }
    }
    // Add command
    parts.push(formatCommandForDisplay(command));
    return parts.join(' ');
}
/**
 * Check if environment variable is standard
 */
function isStandardEnvVar(key) {
    const standardVars = [
        'PATH', 'HOME', 'USER', 'SHELL', 'TERM', 'PWD', 'OLDPWD',
        'LANG', 'LC_ALL', 'TZ', 'TMPDIR', 'TEMP', 'TMP',
        'NODE_ENV', 'npm_config_prefix', 'npm_config_cache',
    ];
    return standardVars.includes(key) || key.startsWith('npm_');
}
/**
 * Truncate command for display in limited space
 */
export function truncateCommand(command, maxLength = 50) {
    const formatted = formatCommandForDisplay(command);
    if (formatted.length <= maxLength) {
        return formatted;
    }
    // Try to keep the command name and truncate arguments
    const [cmd, ...args] = command;
    const cmdFormatted = quoteArgumentIfNeeded(cmd);
    if (cmdFormatted.length >= maxLength - 3) {
        return cmdFormatted.slice(0, maxLength - 3) + '...';
    }
    const remaining = maxLength - cmdFormatted.length - 4; // Space for " ..."
    const argsFormatted = args.map(arg => quoteArgumentIfNeeded(arg)).join(' ');
    if (argsFormatted.length <= remaining) {
        return `${cmdFormatted} ${argsFormatted}`;
    }
    return `${cmdFormatted} ${argsFormatted.slice(0, remaining)}...`;
}
/**
 * Format command exit status
 */
export function formatExitStatus(exitCode) {
    if (exitCode === 0) {
        return 'success';
    }
    else if (exitCode === 1) {
        return 'general error';
    }
    else if (exitCode === 2) {
        return 'misuse of shell builtin';
    }
    else if (exitCode === 126) {
        return 'command not executable';
    }
    else if (exitCode === 127) {
        return 'command not found';
    }
    else if (exitCode === 128) {
        return 'invalid exit argument';
    }
    else if (exitCode > 128 && exitCode <= 165) {
        const signal = exitCode - 128;
        return `terminated by signal ${signal}`;
    }
    else {
        return `exit code ${exitCode}`;
    }
}
/**
 * Format command duration
 */
export function formatDuration(milliseconds) {
    if (milliseconds < 1000) {
        return `${milliseconds}ms`;
    }
    else if (milliseconds < 60000) {
        return `${(milliseconds / 1000).toFixed(1)}s`;
    }
    else {
        const minutes = Math.floor(milliseconds / 60000);
        const seconds = Math.floor((milliseconds % 60000) / 1000);
        return `${minutes}m ${seconds}s`;
    }
}
/**
 * Check if command is potentially dangerous
 */
export function isDangerousCommand(command) {
    if (!command || command.length === 0) {
        return false;
    }
    const dangerousCommands = [
        'rm', 'rmdir', 'del', 'rd',
        'format', 'fdisk', 'mkfs',
        'dd', 'shred', 'wipe',
        'sudo', 'su', 'chmod', 'chown',
        'kill', 'killall', 'pkill',
        'shutdown', 'reboot', 'halt',
        'mv', 'move', 'ren', 'rename',
    ];
    const cmd = command[0].toLowerCase();
    const baseName = cmd.split('/').pop() || cmd;
    return dangerousCommands.includes(baseName);
}
/**
 * Get command category for display
 */
export function getCommandCategory(command) {
    if (!command || command.length === 0) {
        return 'unknown';
    }
    const cmd = command[0].toLowerCase();
    const baseName = cmd.split('/').pop() || cmd;
    // File operations
    if (['ls', 'dir', 'find', 'locate', 'cat', 'head', 'tail', 'less', 'more'].includes(baseName)) {
        return 'file-read';
    }
    if (['cp', 'copy', 'mv', 'move', 'rm', 'del', 'mkdir', 'rmdir'].includes(baseName)) {
        return 'file-write';
    }
    // Git operations
    if (['git'].includes(baseName)) {
        return 'git';
    }
    // Package managers
    if (['npm', 'yarn', 'pnpm', 'pip', 'cargo', 'go'].includes(baseName)) {
        return 'package-manager';
    }
    // System operations
    if (['ps', 'top', 'htop', 'kill', 'killall', 'systemctl', 'service'].includes(baseName)) {
        return 'system';
    }
    // Network operations
    if (['curl', 'wget', 'ping', 'ssh', 'scp', 'rsync'].includes(baseName)) {
        return 'network';
    }
    return 'general';
}
//# sourceMappingURL=format-command.js.map