/**
 * Version Management for Kritrima AI CLI
 *
 * Provides dynamic version loading from package.json
 */
/**
 * Get the current CLI version from package.json
 */
export declare function getVersion(): string;
/**
 * CLI version constant for easy access
 */
export declare const CLI_VERSION: string;
/**
 * Get version information including build details
 */
export declare function getVersionInfo(): {
    version: string;
    nodeVersion: string;
    platform: string;
    arch: string;
};
/**
 * Format version information for display
 */
export declare function formatVersionInfo(): string;
export declare const CLI_NAME = "Kritrima AI CLI";
export declare const CLI_DESCRIPTION = "Advanced AI-powered command line interface";
export declare const BUILD_DATE: string;
export declare const BUILD_COMMIT: string;
export declare const BUILD_BRANCH: string;
export declare const FEATURES: {
    readonly MULTILINE_INPUT: true;
    readonly FILE_TAG_EXPANSION: true;
    readonly SLASH_COMMANDS: true;
    readonly COMMAND_APPROVAL: true;
    readonly SYNTAX_HIGHLIGHTING: true;
    readonly AUTO_COMPLETION: true;
    readonly HISTORY_MANAGEMENT: true;
    readonly SESSION_PERSISTENCE: true;
    readonly DESKTOP_NOTIFICATIONS: true;
    readonly GIT_INTEGRATION: true;
    readonly SANDBOX_EXECUTION: true;
    readonly STREAMING_RESPONSES: true;
    readonly DIFF_VIEWER: true;
    readonly BUG_REPORTING: true;
    readonly UPDATE_CHECKING: true;
};
export declare const API_VERSION = "1.0";
export declare const MIN_NODE_VERSION = "18.0.0";
export declare const SUPPORTED_PLATFORMS: readonly ["win32", "darwin", "linux"];
export declare const REPOSITORY: {
    readonly URL: "https://github.com/kritrima/kritrima-ai-cli";
    readonly ISSUES_URL: "https://github.com/kritrima/kritrima-ai-cli/issues";
    readonly DOCS_URL: "https://docs.kritrima.ai/cli";
    readonly CHANGELOG_URL: "https://github.com/kritrima/kritrima-ai-cli/blob/main/CHANGELOG.md";
};
export declare const LICENSE = "MIT";
export declare const COPYRIGHT: string;
/**
 * Get full version string
 */
export declare function getVersionString(): string;
/**
 * Get detailed version information
 */
export declare function getDetailedVersionInfo(): {
    name: string;
    version: string;
    description: string;
    buildDate: string;
    buildCommit: string;
    buildBranch: string;
    nodeVersion: string;
    platform: string;
    arch: string;
};
/**
 * Get feature availability
 */
export declare function getFeatures(): typeof FEATURES;
/**
 * Check if feature is enabled
 */
export declare function isFeatureEnabled(feature: keyof typeof FEATURES): boolean;
/**
 * Get platform compatibility info
 */
export declare function getPlatformInfo(): {
    supported: boolean;
    platform: string;
    arch: string;
    nodeVersion: string;
    minNodeVersion: string;
};
/**
 * Check Node.js version compatibility
 */
export declare function checkNodeCompatibility(): {
    compatible: boolean;
    current: string;
    minimum: string;
    message?: string;
};
/**
 * Get CLI banner
 */
export declare function getBanner(): string;
//# sourceMappingURL=version.d.ts.map