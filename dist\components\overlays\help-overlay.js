/**
 * Help Overlay Component
 *
 * Displays comprehensive help information including keyboard shortcuts,
 * slash commands, and usage instructions.
 */
import blessed from 'blessed';
import { SLASH_COMMANDS } from '../../utils/slash-commands.js';
export class HelpOverlay {
    screen;
    container;
    contentBox;
    options;
    constructor(options) {
        this.options = options;
        this.screen = options.parent;
        this.createComponents();
        this.setupEventHandlers();
        this.buildContent();
    }
    /**
     * Create UI components
     */
    createComponents() {
        // Main container
        this.container = blessed.box({
            parent: this.screen,
            top: 'center',
            left: 'center',
            width: '90%',
            height: '90%',
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'cyan',
                },
            },
            label: ' Kritrima AI CLI - Help ',
            tags: true,
            keys: true,
            vi: true,
        });
        // Content box
        this.contentBox = blessed.box({
            parent: this.container,
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            scrollable: true,
            alwaysScroll: true,
            scrollbar: {
                ch: ' ',
                track: {
                    bg: 'cyan',
                },
                style: {
                    inverse: true,
                },
            },
            keys: true,
            vi: true,
            mouse: true,
            tags: true,
        });
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Close handlers
        this.container.key(['escape', 'q'], () => {
            this.close();
        });
        // Scroll handlers
        this.contentBox.key(['j', 'down'], () => {
            this.contentBox.scroll(1);
            this.screen.render();
        });
        this.contentBox.key(['k', 'up'], () => {
            this.contentBox.scroll(-1);
            this.screen.render();
        });
        this.contentBox.key(['pagedown'], () => {
            this.contentBox.scroll(10);
            this.screen.render();
        });
        this.contentBox.key(['pageup'], () => {
            this.contentBox.scroll(-10);
            this.screen.render();
        });
        this.contentBox.key(['home'], () => {
            this.contentBox.scrollTo(0);
            this.screen.render();
        });
        this.contentBox.key(['end'], () => {
            this.contentBox.scrollTo(this.contentBox.getScrollHeight());
            this.screen.render();
        });
        // Focus management
        this.contentBox.focus();
    }
    /**
     * Build help content
     */
    buildContent() {
        const content = [
            '{center}{bold}{yellow-fg}Kritrima AI CLI - Comprehensive Help{/yellow-fg}{/bold}{/center}',
            '',
            '{bold}{cyan-fg}OVERVIEW{/cyan-fg}{/bold}',
            'Kritrima AI CLI is a sophisticated AI coding assistant with autonomous agent',
            'capabilities, multi-modal input support, and advanced terminal interface.',
            '',
            '{bold}{cyan-fg}BASIC USAGE{/cyan-fg}{/bold}',
            '• Type your message and press Enter to send',
            '• Use @ prefix for file references: @src/file.ts',
            '• Use / prefix for slash commands: /help, /model, etc.',
            '• Press Ctrl+C to exit or cancel current operation',
            '',
            '{bold}{cyan-fg}KEYBOARD SHORTCUTS{/cyan-fg}{/bold}',
            '',
            '{bold}Global Shortcuts:{/bold}',
            '  Ctrl+C        Exit application or cancel operation',
            '  Ctrl+L        Clear conversation',
            '  Ctrl+H        Show this help',
            '  Ctrl+M        Open model selection',
            '  Ctrl+R        Open command history',
            '  Escape        Close current overlay',
            '',
            '{bold}Input Shortcuts:{/bold}',
            '  Enter         Send message (single line) or new line (multiline)',
            '  Ctrl+Enter    Force new line in multiline mode',
            '  Tab           Auto-complete files/commands',
            '  Up/Down       Navigate command history',
            '  Ctrl+A        Move to beginning of line',
            '  Ctrl+E        Move to end of line',
            '  Ctrl+K        Delete to end of line',
            '  Ctrl+U        Delete entire line',
            '',
            '{bold}Navigation:{/bold}',
            '  j/Down        Scroll down',
            '  k/Up          Scroll up',
            '  Page Down     Scroll down (page)',
            '  Page Up       Scroll up (page)',
            '  Home          Go to top',
            '  End           Go to bottom',
            '',
            '{bold}{cyan-fg}SLASH COMMANDS{/cyan-fg}{/bold}',
            '',
            ...SLASH_COMMANDS.map(cmd => `  {yellow-fg}${cmd.command.padEnd(12)}{/yellow-fg} ${cmd.description}`),
            '',
            '{bold}{cyan-fg}FILE REFERENCES{/cyan-fg}{/bold}',
            '',
            'Use @ prefix to reference files in your messages:',
            '  @README.md              Include README.md content',
            '  @src/components/        Reference directory',
            '  @package.json           Include package.json',
            '',
            'File references are automatically expanded to include file contents',
            'in XML blocks for the AI to understand your codebase context.',
            '',
            '{bold}{cyan-fg}APPROVAL MODES{/cyan-fg}{/bold}',
            '',
            '{bold}suggest{/bold}     Manual approval required for all commands',
            '{bold}auto-edit{/bold}   Automatic file edits, manual command approval',
            '{bold}full-auto{/bold}   Automatic everything (use with caution)',
            '',
            '{bold}{cyan-fg}PROVIDERS & MODELS{/cyan-fg}{/bold}',
            '',
            'Supported AI providers:',
            '• OpenAI (GPT-4, GPT-3.5, etc.)',
            '• Azure OpenAI',
            '• Google Gemini',
            '• Ollama (local models)',
            '• Mistral AI',
            '• DeepSeek',
            '• xAI (Grok)',
            '• Groq',
            '• ArceeAI',
            '• OpenRouter',
            '',
            'Use /model command or Ctrl+M to switch providers and models.',
            '',
            '{bold}{cyan-fg}CONFIGURATION{/cyan-fg}{/bold}',
            '',
            'Configuration files (JSON or YAML):',
            '• ~/.kritrima-ai/config.json (global)',
            '• ./.kritrima-ai/config.json (project)',
            '',
            'Environment variables:',
            '• OPENAI_API_KEY, ANTHROPIC_API_KEY, etc.',
            '• PROVIDER_API_KEY for custom providers',
            '• PROVIDER_BASE_URL for custom endpoints',
            '',
            '{bold}{cyan-fg}ADVANCED FEATURES{/cyan-fg}{/bold}',
            '',
            '• Multi-modal input (text + images)',
            '• Real-time streaming responses',
            '• Autonomous agent with tool calling',
            '• Sandboxed command execution',
            '• Session persistence and history',
            '• Git integration and diff viewing',
            '• Desktop notifications',
            '• Automatic updates',
            '',
            '{bold}{cyan-fg}EXAMPLES{/cyan-fx}{/bold}',
            '',
            'Basic usage:',
            '  "Help me debug this error in @src/app.ts"',
            '  "Create a new React component for user authentication"',
            '  "Explain the code in @README.md"',
            '',
            'With commands:',
            '  "/model" - Switch to different AI model',
            '  "/history" - View previous commands',
            '  "/diff" - Show git changes',
            '  "/clear" - Clear conversation',
            '',
            '{bold}{cyan-fg}TROUBLESHOOTING{/cyan-fx}{/bold}',
            '',
            '• Check API keys are set correctly',
            '• Verify internet connection for cloud providers',
            '• Use /bug command to generate bug reports',
            '• Check logs in ~/.local/oai-codex/ (Linux) or temp directory',
            '• Try different models if one is not responding',
            '',
            '{center}{gray-fg}Press Escape or Q to close this help{/gray-fg}{/center}',
        ];
        this.contentBox.setContent(content.join('\n'));
    }
    /**
     * Show the overlay
     */
    show() {
        this.container.show();
        this.contentBox.focus();
        this.screen.render();
    }
    /**
     * Hide the overlay
     */
    hide() {
        this.container.hide();
        this.screen.render();
    }
    /**
     * Close the overlay
     */
    close() {
        this.hide();
        if (this.options.onClose) {
            this.options.onClose();
        }
    }
    /**
     * Destroy the overlay
     */
    destroy() {
        this.container.destroy();
    }
}
export default HelpOverlay;
//# sourceMappingURL=help-overlay.js.map