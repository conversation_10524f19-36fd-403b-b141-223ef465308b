/**
 * Terminal Chat Interface
 *
 * Main terminal-based chat interface using blessed.js for rich UI.
 * Handles real-time conversation, model switching, and overlays.
 */
import blessed from 'blessed';
import { EventEmitter } from 'events';
import { AgentLoop } from '../../utils/agent/agent-loop.js';
import { createInputItem } from '../../utils/input-utils.js';
import { logInfo, logError, logDebug, fpsDebugger } from '../../utils/logger/log.js';
import { saveRollout } from '../../utils/storage/save-rollout.js';
import { addToHistory } from '../../utils/storage/command-history.js';
import { HistoryOverlay, ModelOverlay, HelpOverlay, ApprovalModeOverlay, DiffOverlay, SessionsOverlay } from '../overlays/index.js';
import { nanoid } from 'nanoid';
export class TerminalChat extends EventEmitter {
    config;
    screen;
    chatContainer;
    inputBox;
    statusBar;
    agentLoop;
    // State
    sessionId;
    items = [];
    currentModel;
    currentProvider;
    approvalPolicy;
    overlayMode = 'none';
    loading = false;
    terminalSize;
    // UI Elements
    currentOverlay = null;
    notificationTimeout = null;
    constructor(config, initialPrompt) {
        super();
        this.config = config;
        this.sessionId = nanoid();
        this.currentModel = config.model;
        this.currentProvider = config.provider;
        this.approvalPolicy = config.approvalMode;
        this.terminalSize = {
            columns: process.stdout.columns || 80,
            rows: process.stdout.rows || 24,
        };
        // Initialize UI
        this.initializeScreen();
        this.initializeComponents();
        this.setupEventHandlers();
        // Initialize agent loop
        this.agentLoop = new AgentLoop({
            model: this.currentModel,
            provider: this.currentProvider,
            approvalPolicy: this.approvalPolicy,
        });
        // Process initial prompt if provided
        if (initialPrompt) {
            this.processInitialPrompt(initialPrompt);
        }
        logInfo(`Terminal chat initialized with session ID: ${this.sessionId}`);
    }
    /**
     * Initialize the blessed screen
     */
    initializeScreen() {
        this.screen = blessed.screen({
            smartCSR: true,
            title: 'Kritrima AI CLI',
            cursor: {
                artificial: true,
                shape: 'line',
                blink: true,
            },
            debug: this.config.debug,
        });
        // Handle screen resize
        this.screen.on('resize', () => {
            this.terminalSize = {
                columns: this.screen.width,
                rows: this.screen.height,
            };
            this.handleResize();
        });
    }
    /**
     * Initialize UI components
     */
    initializeComponents() {
        // Chat container
        this.chatContainer = blessed.box({
            parent: this.screen,
            top: 0,
            left: 0,
            width: '100%',
            height: '100%-4',
            scrollable: true,
            alwaysScroll: true,
            scrollbar: {
                ch: ' ',
                track: {
                    bg: 'cyan',
                },
                style: {
                    inverse: true,
                },
            },
            border: {
                type: 'line',
            },
            style: {
                fg: 'white',
                border: {
                    fg: 'cyan',
                },
            },
            tags: true,
            mouse: true,
        });
        // Input box
        this.inputBox = blessed.textarea({
            parent: this.screen,
            bottom: 1,
            left: 0,
            width: '100%',
            height: 3,
            border: {
                type: 'line',
            },
            style: {
                fg: 'white',
                border: {
                    fg: 'cyan',
                },
            },
            inputOnFocus: true,
            scrollable: true,
            keys: true,
            mouse: true,
            tags: false,
        });
        // Status bar
        this.statusBar = blessed.box({
            parent: this.screen,
            bottom: 0,
            left: 0,
            width: '100%',
            height: 1,
            style: {
                fg: 'white',
                bg: 'blue',
            },
            tags: true,
        });
        this.updateStatusBar();
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Global key handlers
        this.screen.key(['escape', 'C-c'], () => {
            if (this.overlayMode !== 'none') {
                this.closeOverlay();
            }
            else {
                this.shutdown();
            }
        });
        this.screen.key(['C-l'], () => {
            this.clearConversation();
        });
        this.screen.key(['C-h'], () => {
            this.showOverlay('help');
        });
        this.screen.key(['C-m'], () => {
            this.showOverlay('model');
        });
        this.screen.key(['C-r'], () => {
            this.showOverlay('history');
        });
        // Input box handlers
        this.inputBox.key(['enter'], () => {
            this.handleInput();
        });
        this.inputBox.key(['C-enter'], () => {
            this.inputBox.insertText('\n');
        });
        this.inputBox.key(['tab'], () => {
            this.handleTabCompletion();
        });
        // Chat container scroll
        this.chatContainer.key(['j', 'down'], () => {
            this.chatContainer.scroll(1);
            this.screen.render();
        });
        this.chatContainer.key(['k', 'up'], () => {
            this.chatContainer.scroll(-1);
            this.screen.render();
        });
        // Focus management
        this.inputBox.focus();
    }
    /**
     * Start the terminal chat interface
     */
    async start() {
        try {
            // Display welcome message
            this.addSystemMessage('Welcome to Kritrima AI CLI! Type your message or use slash commands.');
            this.addSystemMessage('Available commands: /help, /model, /history, /clear, /approval');
            // Render initial screen
            this.screen.render();
            // Start FPS debugging if enabled
            if (this.config.debug) {
                this.startFPSDebugging();
            }
            logInfo('Terminal chat interface started');
        }
        catch (error) {
            logError('Failed to start terminal chat', error);
            throw error;
        }
    }
    /**
     * Handle user input
     */
    async handleInput() {
        const input = this.inputBox.getValue().trim();
        if (!input) {
            return;
        }
        // Clear input
        this.inputBox.clearValue();
        this.screen.render();
        // Add to history
        addToHistory(input, this.sessionId);
        // Handle slash commands
        if (input.startsWith('/')) {
            this.handleSlashCommand(input);
            return;
        }
        // Process as AI input
        await this.processUserInput(input);
    }
    /**
     * Handle slash commands
     */
    handleSlashCommand(command) {
        const [cmd, ...args] = command.split(' ');
        switch (cmd.toLowerCase()) {
            case '/help':
                this.showOverlay('help');
                break;
            case '/model':
                this.showOverlay('model');
                break;
            case '/history':
                this.showOverlay('history');
                break;
            case '/sessions':
                this.showOverlay('sessions');
                break;
            case '/approval':
                this.showOverlay('approval');
                break;
            case '/diff':
                this.showOverlay('diff');
                break;
            case '/clear':
                this.clearConversation();
                break;
            case '/compact':
                this.compactConversation();
                break;
            case '/bug':
                this.generateBugReport();
                break;
            default:
                this.addSystemMessage(`Unknown command: ${cmd}. Type /help for available commands.`);
        }
    }
    /**
     * Process user input through AI
     */
    async processUserInput(input) {
        try {
            this.setLoading(true);
            // Add user message to chat
            const userMessage = {
                id: nanoid(),
                type: 'message',
                timestamp: Date.now(),
                content: [{ type: 'input_text', text: input }],
            };
            this.addMessage(userMessage);
            // Create input item for agent
            const inputItem = await createInputItem(input);
            // Process through agent loop
            const response = await this.agentLoop.processInput(inputItem);
            // Add response to chat
            this.addMessage(response);
            // Save session
            this.saveSession();
        }
        catch (error) {
            const err = error;
            logError('Failed to process user input', err);
            this.addSystemMessage(`Error: ${err.message}`);
        }
        finally {
            this.setLoading(false);
        }
    }
    /**
     * Add message to chat
     */
    addMessage(message) {
        this.items.push(message);
        this.renderMessage(message);
        this.scrollToBottom();
        this.screen.render();
    }
    /**
     * Add system message
     */
    addSystemMessage(text) {
        const message = {
            id: nanoid(),
            type: 'system',
            timestamp: Date.now(),
            content: [{ type: 'input_text', text }],
        };
        this.addMessage(message);
    }
    /**
     * Render a message in the chat container
     */
    renderMessage(message) {
        const timestamp = new Date(message.timestamp).toLocaleTimeString();
        let content = '';
        switch (message.type) {
            case 'message':
                if (message.content[0]?.type === 'input_text') {
                    content = `{cyan-fg}[${timestamp}] User:{/cyan-fg} ${message.content[0].text}`;
                }
                break;
            case 'system':
                if (message.content[0]?.type === 'input_text') {
                    content = `{yellow-fg}[${timestamp}] System:{/yellow-fg} ${message.content[0].text}`;
                }
                break;
            case 'error':
                if (message.content[0]?.type === 'input_text') {
                    content = `{red-fg}[${timestamp}] Error:{/red-fg} ${message.content[0].text}`;
                }
                break;
        }
        if (content) {
            this.chatContainer.insertLine(this.chatContainer.getLines().length, content);
        }
    }
    /**
     * Update status bar
     */
    updateStatusBar() {
        const status = [
            `Model: ${this.currentModel}`,
            `Provider: ${this.currentProvider}`,
            `Approval: ${this.approvalPolicy}`,
            `Messages: ${this.items.length}`,
        ].join(' | ');
        this.statusBar.setContent(status);
    }
    /**
     * Set loading state
     */
    setLoading(loading) {
        this.loading = loading;
        this.updateStatusBar();
        if (loading) {
            this.statusBar.setContent(this.statusBar.getContent() + ' | {yellow-fg}Thinking...{/yellow-fg}');
        }
        this.screen.render();
    }
    /**
     * Scroll to bottom of chat
     */
    scrollToBottom() {
        this.chatContainer.setScrollPerc(100);
    }
    /**
     * Handle screen resize
     */
    handleResize() {
        logDebug(`Screen resized to ${this.terminalSize.columns}x${this.terminalSize.rows}`);
        this.screen.render();
    }
    /**
     * Start FPS debugging
     */
    startFPSDebugging() {
        setInterval(() => {
            fpsDebugger.frame();
        }, 16); // ~60 FPS
    }
    /**
     * Process initial prompt
     */
    async processInitialPrompt(prompt) {
        // Delay to allow UI to initialize
        setTimeout(async () => {
            await this.processUserInput(prompt);
        }, 100);
    }
    /**
     * Show overlay based on mode
     */
    showOverlay(mode) {
        // Close any existing overlay
        this.closeOverlay();
        this.overlayMode = mode;
        switch (mode) {
            case 'history':
                this.showHistoryOverlay();
                break;
            case 'model':
                this.showModelOverlay();
                break;
            case 'help':
                this.showHelpOverlay();
                break;
            case 'approval':
                this.showApprovalOverlay();
                break;
            case 'diff':
                this.showDiffOverlay();
                break;
            case 'sessions':
                this.showSessionsOverlay();
                break;
            default:
                logDebug(`Unknown overlay mode: ${mode}`);
        }
    }
    closeOverlay() {
        this.overlayMode = 'none';
        if (this.currentOverlay) {
            this.currentOverlay.destroy();
            this.currentOverlay = null;
        }
        this.screen.render();
    }
    clearConversation() {
        this.items = [];
        this.chatContainer.setContent('');
        this.addSystemMessage('Conversation cleared.');
    }
    compactConversation() {
        this.addSystemMessage('Conversation compacted (feature coming soon).');
    }
    generateBugReport() {
        this.addSystemMessage('Bug report generation (feature coming soon).');
    }
    handleTabCompletion() {
        // Tab completion implementation
        logDebug('Tab completion triggered');
    }
    saveSession() {
        try {
            saveRollout(this.sessionId, this.items);
        }
        catch (error) {
            logError('Failed to save session', error);
        }
    }
    /**
     * Show history overlay
     */
    showHistoryOverlay() {
        this.currentOverlay = new HistoryOverlay({
            parent: this.screen,
            onSelect: (command) => {
                this.inputBox.setValue(command);
                this.closeOverlay();
            },
            onClose: () => {
                this.closeOverlay();
            },
        });
        this.currentOverlay.show();
    }
    /**
     * Show model overlay
     */
    showModelOverlay() {
        this.currentOverlay = new ModelOverlay({
            parent: this.screen,
            currentProvider: this.currentProvider,
            currentModel: this.currentModel,
            onSelect: (provider, model) => {
                this.currentProvider = provider;
                this.currentModel = model;
                this.updateStatusBar();
                this.closeOverlay();
            },
            onClose: () => {
                this.closeOverlay();
            },
        });
        this.currentOverlay.show();
    }
    /**
     * Show help overlay
     */
    showHelpOverlay() {
        this.currentOverlay = new HelpOverlay({
            parent: this.screen,
            onClose: () => {
                this.closeOverlay();
            },
        });
        this.currentOverlay.show();
    }
    /**
     * Show approval overlay
     */
    showApprovalOverlay() {
        this.currentOverlay = new ApprovalModeOverlay({
            parent: this.screen,
            currentMode: this.approvalPolicy,
            onSelect: (mode) => {
                this.approvalPolicy = mode;
                this.updateStatusBar();
                this.closeOverlay();
            },
            onClose: () => {
                this.closeOverlay();
            },
        });
        this.currentOverlay.show();
    }
    /**
     * Show diff overlay
     */
    showDiffOverlay() {
        this.currentOverlay = new DiffOverlay({
            parent: this.screen,
            workingDirectory: this.config.workingDirectory,
            onClose: () => {
                this.closeOverlay();
            },
        });
        this.currentOverlay.show();
    }
    /**
     * Show sessions overlay
     */
    showSessionsOverlay() {
        this.currentOverlay = new SessionsOverlay({
            parent: this.screen,
            onLoad: (sessionData) => {
                this.loadSession(sessionData);
                this.closeOverlay();
            },
            onClose: () => {
                this.closeOverlay();
            },
        });
        this.currentOverlay.show();
    }
    /**
     * Load session data
     */
    loadSession(sessionData) {
        try {
            this.items = sessionData.items;
            this.sessionId = sessionData.id;
            // Update configuration
            if (sessionData.config.model) {
                this.currentModel = sessionData.config.model;
            }
            if (sessionData.config.provider) {
                this.currentProvider = sessionData.config.provider;
            }
            if (sessionData.config.approvalMode) {
                this.approvalPolicy = sessionData.config.approvalMode;
            }
            // Re-render chat
            this.chatContainer.setContent('');
            for (const item of this.items) {
                this.renderMessage(item);
            }
            this.updateStatusBar();
            this.scrollToBottom();
            this.screen.render();
            this.addSystemMessage(`Session loaded: ${sessionData.metadata.messageCount} messages`);
            logInfo(`Loaded session ${sessionData.id}`);
        }
        catch (error) {
            const err = error;
            logError('Failed to load session', err);
            this.addSystemMessage(`Error loading session: ${err.message}`);
        }
    }
    /**
     * Shutdown the terminal chat
     */
    shutdown() {
        try {
            this.saveSession();
            this.screen.destroy();
            process.exit(0);
        }
        catch (error) {
            logError('Error during shutdown', error);
            process.exit(1);
        }
    }
}
//# sourceMappingURL=terminal-chat.js.map