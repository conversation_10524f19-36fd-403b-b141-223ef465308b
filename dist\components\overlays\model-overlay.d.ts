/**
 * Model Overlay Component
 *
 * Allows users to select AI models and providers with real-time model discovery.
 */
import blessed from 'blessed';
export interface ModelOverlayOptions {
    parent: blessed.Widgets.Screen;
    currentProvider: string;
    currentModel: string;
    onSelect?: (provider: string, model: string) => void;
    onClose?: () => void;
}
export declare class ModelOverlay {
    private screen;
    private container;
    private providerList;
    private modelList;
    private statusBar;
    private providers;
    private models;
    private selectedProvider;
    private selectedModel;
    private options;
    private loading;
    constructor(options: ModelOverlayOptions);
    /**
     * Load available providers
     */
    private loadProviders;
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Update provider list
     */
    private updateProviderList;
    /**
     * Handle provider selection
     */
    private onProviderSelect;
    /**
     * Load models for current provider
     */
    private loadModels;
    /**
     * Update model list
     */
    private updateModelList;
    /**
     * Select current item
     */
    private selectCurrent;
    /**
     * Show the overlay
     */
    show(): void;
    /**
     * Hide the overlay
     */
    hide(): void;
    /**
     * Close the overlay
     */
    close(): void;
    /**
     * Destroy the overlay
     */
    destroy(): void;
}
export default ModelOverlay;
//# sourceMappingURL=model-overlay.d.ts.map