/**
 * History Overlay Component
 *
 * Displays command history with search functionality and selection.
 */
import blessed from 'blessed';
export interface HistoryOverlayOptions {
    parent: blessed.Widgets.Screen;
    onSelect?: (command: string) => void;
    onClose?: () => void;
}
export declare class HistoryOverlay {
    private screen;
    private container;
    private list;
    private searchBox;
    private statusBar;
    private history;
    private filteredHistory;
    private searchTerm;
    private options;
    constructor(options: HistoryOverlayOptions);
    /**
     * Load command history
     */
    private loadHistory;
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Filter history based on search term
     */
    private filterHistory;
    /**
     * Update list display
     */
    private updateList;
    /**
     * Select current item
     */
    private selectCurrent;
    /**
     * Clear all history
     */
    private clearHistory;
    /**
     * Show the overlay
     */
    show(): void;
    /**
     * Hide the overlay
     */
    hide(): void;
    /**
     * Close the overlay
     */
    close(): void;
    /**
     * Destroy the overlay
     */
    destroy(): void;
}
export default HistoryOverlay;
//# sourceMappingURL=history-overlay.d.ts.map