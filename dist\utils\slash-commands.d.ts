/**
 * Slash Commands System
 *
 * Defines and manages slash commands for the terminal interface.
 * Provides auto-completion and command processing capabilities.
 */
export interface SlashCommand {
    command: string;
    description: string;
    aliases?: string[];
    parameters?: string[];
    category?: string;
    hidden?: boolean;
}
/**
 * Available slash commands
 */
export declare const SLASH_COMMANDS: SlashCommand[];
/**
 * Get all slash commands
 */
export declare function getSlashCommands(includeHidden?: boolean): SlashCommand[];
/**
 * Get slash commands by category
 */
export declare function getSlashCommandsByCategory(includeHidden?: boolean): Record<string, SlashCommand[]>;
/**
 * Find slash command by name or alias
 */
export declare function findSlashCommand(input: string): SlashCommand | null;
/**
 * Get auto-completion suggestions for slash commands
 */
export declare function getSlashCommandSuggestions(input: string): SlashCommand[];
/**
 * Parse slash command with parameters
 */
export declare function parseSlashCommand(input: string): {
    command: SlashCommand | null;
    parameters: string[];
    rawInput: string;
};
/**
 * Validate slash command parameters
 */
export declare function validateSlashCommand(command: SlashCommand, parameters: string[]): {
    valid: boolean;
    errors: string[];
};
/**
 * Format slash command help text
 */
export declare function formatSlashCommandHelp(command: SlashCommand): string;
/**
 * Generate help text for all commands
 */
export declare function generateHelpText(): string;
/**
 * Check if input is a slash command
 */
export declare function isSlashCommand(input: string): boolean;
/**
 * Get command usage string
 */
export declare function getCommandUsage(command: SlashCommand): string;
//# sourceMappingURL=slash-commands.d.ts.map