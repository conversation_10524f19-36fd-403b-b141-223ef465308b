/**
 * Git Diff Utilities
 *
 * Provides Git integration for generating diffs, checking status,
 * and understanding repository state.
 */
import type { DiffResult } from '../types/index.js';
export interface GitStatus {
    branch: string;
    ahead: number;
    behind: number;
    staged: string[];
    unstaged: string[];
    untracked: string[];
    conflicted: string[];
}
export interface GitDiffOptions {
    staged?: boolean;
    cached?: boolean;
    nameOnly?: boolean;
    stat?: boolean;
    unified?: number;
    ignoreWhitespace?: boolean;
    files?: string[];
}
/**
 * Get git diff for current working directory
 */
export declare function getGitDiff(workingDirectory?: string, options?: GitDiffOptions): Promise<string>;
/**
 * Get git status information
 */
export declare function getGitStatus(workingDirectory?: string): Promise<GitStatus>;
/**
 * Get detailed diff result with file information
 */
export declare function getDetailedDiff(workingDirectory?: string, options?: GitDiffOptions): Promise<DiffResult>;
/**
 * Check if there are uncommitted changes
 */
export declare function hasUncommittedChanges(workingDirectory?: string): Promise<boolean>;
/**
 * Get current git branch
 */
export declare function getCurrentBranch(workingDirectory?: string): Promise<string>;
/**
 * Get git repository root
 */
export declare function getGitRoot(workingDirectory?: string): Promise<string>;
/**
 * Format diff for display
 */
export declare function formatDiffForDisplay(diff: string): string;
/**
 * Get short status summary
 */
export declare function getShortStatus(workingDirectory?: string): Promise<string>;
//# sourceMappingURL=get-diff.d.ts.map