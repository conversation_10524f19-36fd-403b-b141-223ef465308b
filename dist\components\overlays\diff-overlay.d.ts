/**
 * Diff Overlay Component
 *
 * Displays git diff information with syntax highlighting and navigation.
 */
import blessed from 'blessed';
export interface DiffOverlayOptions {
    parent: blessed.Widgets.Screen;
    workingDirectory?: string;
    onClose?: () => void;
}
export declare class DiffOverlay {
    private screen;
    private container;
    private diffBox;
    private statusBar;
    private options;
    private loading;
    constructor(options: DiffOverlayOptions);
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Load git diff
     */
    private loadDiff;
    /**
     * Display diff content with syntax highlighting
     */
    private displayDiff;
    /**
     * Display a message when no diff or error
     */
    private displayMessage;
    /**
     * Update status bar
     */
    private updateStatus;
    /**
     * Show the overlay
     */
    show(): void;
    /**
     * Hide the overlay
     */
    hide(): void;
    /**
     * Close the overlay
     */
    close(): void;
    /**
     * Destroy the overlay
     */
    destroy(): void;
}
export default DiffOverlay;
//# sourceMappingURL=diff-overlay.d.ts.map