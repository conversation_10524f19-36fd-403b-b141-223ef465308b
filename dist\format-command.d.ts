/**
 * Command Formatting Utilities
 *
 * Provides intelligent command display formatting with shell quoting,
 * wrapper detection, and user-friendly presentation.
 */
/**
 * Format command array for display
 */
export declare function formatCommandForDisplay(command: string[]): string;
/**
 * Format command with working directory context
 */
export declare function formatCommandWithContext(command: string[], workdir?: string, currentDir?: string): string;
/**
 * Format command for logging
 */
export declare function formatCommandForLogging(command: string[], workdir?: string, env?: Record<string, string>): string;
/**
 * Truncate command for display in limited space
 */
export declare function truncateCommand(command: string[], maxLength?: number): string;
/**
 * Format command exit status
 */
export declare function formatExitStatus(exitCode: number): string;
/**
 * Format command duration
 */
export declare function formatDuration(milliseconds: number): string;
/**
 * Check if command is potentially dangerous
 */
export declare function isDangerousCommand(command: string[]): boolean;
/**
 * Get command category for display
 */
export declare function getCommandCategory(command: string[]): string;
//# sourceMappingURL=format-command.d.ts.map