/**
 * Command Approval System
 *
 * Handles command approval workflow with different policies,
 * safety checks, and user interaction for command execution.
 */
import { EventEmitter } from 'events';
import type { ApprovalPolicy, ExecInput } from './types/index.js';
export declare enum ReviewDecision {
    YES = "yes",
    NO_CONTINUE = "no_continue",
    NO_EXIT = "no_exit",
    ALWAYS = "always",
    EXPLAIN = "explain"
}
export interface ApprovalRequest {
    id: string;
    command: string[];
    workdir: string;
    explanation?: string;
    category: string;
    isDangerous: boolean;
    timestamp: number;
}
export interface ApprovalResult {
    decision: ReviewDecision;
    reason?: string;
    alwaysApprove?: boolean;
}
export interface ApprovalConfig {
    policy: ApprovalPolicy;
    safeCommands: string[];
    dangerousCommands: string[];
    allowedPaths: string[];
    blockedPaths: string[];
    autoApprovePatterns: string[];
    requireExplanation: boolean;
}
declare class ApprovalManager extends EventEmitter {
    private config;
    private alwaysApprovedCommands;
    private pendingRequests;
    constructor(config?: Partial<ApprovalConfig>);
    /**
     * Check if command can be auto-approved
     */
    canAutoApprove(execInput: ExecInput): boolean;
    /**
     * Check if command is safe to execute
     */
    isCommandSafe(command: string[], workdir?: string): boolean;
    /**
     * Check if path is safe for operations
     */
    isPathSafe(path: string): boolean;
    /**
     * Request approval for command execution
     */
    requestApproval(execInput: ExecInput, explanation?: string): Promise<ApprovalResult>;
    /**
     * Submit approval decision
     */
    submitApproval(requestId: string, result: ApprovalResult): void;
    /**
     * Get pending approval requests
     */
    getPendingRequests(): ApprovalRequest[];
    /**
     * Cancel approval request
     */
    cancelApproval(requestId: string, reason?: string): void;
    /**
     * Update approval configuration
     */
    updateConfig(newConfig: Partial<ApprovalConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): ApprovalConfig;
    /**
     * Clear always approved commands
     */
    clearAlwaysApproved(): void;
    /**
     * Get always approved commands
     */
    getAlwaysApproved(): string[];
    /**
     * Validate command against security policies
     */
    validateCommand(execInput: ExecInput): {
        valid: boolean;
        warnings: string[];
        errors: string[];
    };
}
/**
 * Get or create global approval manager
 */
export declare function getApprovalManager(config?: Partial<ApprovalConfig>): ApprovalManager;
/**
 * Check if command can be auto-approved
 */
export declare function canAutoApprove(command: string[], approvalPolicy: ApprovalPolicy, safeCommands?: string[]): boolean;
/**
 * Request command approval
 */
export declare function requestCommandApproval(execInput: ExecInput, approvalPolicy: ApprovalPolicy, explanation?: string): Promise<ApprovalResult>;
/**
 * Format approval request for display
 */
export declare function formatApprovalRequest(request: ApprovalRequest): string;
/**
 * Get approval prompt text
 */
export declare function getApprovalPrompt(request: ApprovalRequest): string;
/**
 * Parse approval input
 */
export declare function parseApprovalInput(input: string): ReviewDecision;
/**
 * Create approval configuration from policy
 */
export declare function createApprovalConfig(policy: ApprovalPolicy): ApprovalConfig;
/**
 * Get security assessment for command
 */
export declare function getSecurityAssessment(command: string[]): {
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    reasons: string[];
    recommendations: string[];
};
export default ApprovalManager;
//# sourceMappingURL=approvals.d.ts.map