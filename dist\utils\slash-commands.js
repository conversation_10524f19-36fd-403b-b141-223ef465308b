/**
 * Slash Commands System
 *
 * Defines and manages slash commands for the terminal interface.
 * Provides auto-completion and command processing capabilities.
 */
/**
 * Available slash commands
 */
export const SLASH_COMMANDS = [
    {
        command: '/help',
        description: 'Show help information and available commands',
        aliases: ['/h', '/?'],
        category: 'General',
    },
    {
        command: '/clear',
        description: 'Clear conversation history',
        aliases: ['/cls'],
        category: 'General',
    },
    {
        command: '/compact',
        description: 'Compress conversation context to save tokens',
        category: 'General',
    },
    {
        command: '/model',
        description: 'Open model selection panel',
        aliases: ['/m'],
        category: 'Configuration',
    },
    {
        command: '/provider',
        description: 'Switch AI provider',
        aliases: ['/p'],
        parameters: ['provider-name'],
        category: 'Configuration',
    },
    {
        command: '/approval',
        description: 'Open approval mode selection',
        aliases: ['/a'],
        category: 'Configuration',
    },
    {
        command: '/history',
        description: 'View command history',
        aliases: ['/hist'],
        category: 'Navigation',
    },
    {
        command: '/sessions',
        description: 'Browse previous sessions',
        aliases: ['/sess'],
        category: 'Navigation',
    },
    {
        command: '/diff',
        description: 'Show git diff of current changes',
        aliases: ['/d'],
        category: 'Git',
    },
    {
        command: '/status',
        description: 'Show git status',
        aliases: ['/st'],
        category: 'Git',
    },
    {
        command: '/bug',
        description: 'Generate bug report URL',
        category: 'Support',
    },
    {
        command: '/debug',
        description: 'Toggle debug mode',
        category: 'Development',
        hidden: true,
    },
    {
        command: '/version',
        description: 'Show version information',
        aliases: ['/v'],
        category: 'General',
    },
    {
        command: '/config',
        description: 'Show current configuration',
        category: 'Configuration',
    },
    {
        command: '/exit',
        description: 'Exit the application',
        aliases: ['/quit', '/q'],
        category: 'General',
    },
];
/**
 * Get all slash commands
 */
export function getSlashCommands(includeHidden = false) {
    return SLASH_COMMANDS.filter(cmd => includeHidden || !cmd.hidden);
}
/**
 * Get slash commands by category
 */
export function getSlashCommandsByCategory(includeHidden = false) {
    const commands = getSlashCommands(includeHidden);
    const categories = {};
    for (const command of commands) {
        const category = command.category || 'Other';
        if (!categories[category]) {
            categories[category] = [];
        }
        categories[category].push(command);
    }
    return categories;
}
/**
 * Find slash command by name or alias
 */
export function findSlashCommand(input) {
    const command = input.toLowerCase().trim();
    return SLASH_COMMANDS.find(cmd => {
        if (cmd.command.toLowerCase() === command) {
            return true;
        }
        if (cmd.aliases) {
            return cmd.aliases.some(alias => alias.toLowerCase() === command);
        }
        return false;
    }) || null;
}
/**
 * Get auto-completion suggestions for slash commands
 */
export function getSlashCommandSuggestions(input) {
    const query = input.toLowerCase().trim();
    if (!query.startsWith('/')) {
        return [];
    }
    const searchTerm = query.slice(1); // Remove the '/' prefix
    if (searchTerm === '') {
        return getSlashCommands();
    }
    return SLASH_COMMANDS.filter(cmd => {
        // Match command name
        if (cmd.command.toLowerCase().includes(searchTerm)) {
            return true;
        }
        // Match aliases
        if (cmd.aliases) {
            return cmd.aliases.some(alias => alias.toLowerCase().includes(searchTerm));
        }
        // Match description
        return cmd.description.toLowerCase().includes(searchTerm);
    });
}
/**
 * Parse slash command with parameters
 */
export function parseSlashCommand(input) {
    const trimmed = input.trim();
    const parts = trimmed.split(/\s+/);
    const commandPart = parts[0];
    const parameters = parts.slice(1);
    const command = findSlashCommand(commandPart);
    return {
        command,
        parameters,
        rawInput: trimmed,
    };
}
/**
 * Validate slash command parameters
 */
export function validateSlashCommand(command, parameters) {
    const errors = [];
    if (command.parameters) {
        const requiredParams = command.parameters.filter(p => !p.endsWith('?'));
        if (parameters.length < requiredParams.length) {
            errors.push(`Command ${command.command} requires ${requiredParams.length} parameter(s): ${requiredParams.join(', ')}`);
        }
    }
    return {
        valid: errors.length === 0,
        errors,
    };
}
/**
 * Format slash command help text
 */
export function formatSlashCommandHelp(command) {
    let help = `${command.command}`;
    if (command.parameters) {
        help += ` ${command.parameters.join(' ')}`;
    }
    if (command.aliases && command.aliases.length > 0) {
        help += ` (aliases: ${command.aliases.join(', ')})`;
    }
    help += `\n  ${command.description}`;
    return help;
}
/**
 * Generate help text for all commands
 */
export function generateHelpText() {
    const categories = getSlashCommandsByCategory();
    const sections = [];
    for (const [categoryName, commands] of Object.entries(categories)) {
        sections.push(`\n${categoryName} Commands:`);
        sections.push('─'.repeat(categoryName.length + 10));
        for (const command of commands) {
            sections.push(formatSlashCommandHelp(command));
            sections.push('');
        }
    }
    return sections.join('\n');
}
/**
 * Check if input is a slash command
 */
export function isSlashCommand(input) {
    return input.trim().startsWith('/');
}
/**
 * Get command usage string
 */
export function getCommandUsage(command) {
    let usage = command.command;
    if (command.parameters) {
        usage += ' ' + command.parameters.map(param => {
            if (param.endsWith('?')) {
                return `[${param.slice(0, -1)}]`;
            }
            return `<${param}>`;
        }).join(' ');
    }
    return usage;
}
//# sourceMappingURL=slash-commands.js.map