/**
 * Terminal Chat Command Review Component
 *
 * Provides command approval interface with security assessment,
 * risk analysis, and user decision handling.
 */
import blessed from 'blessed';
import { ReviewDecision, type ApprovalRequest } from '../../approvals.js';
export interface CommandReviewOptions {
    parent: blessed.Widgets.Node;
    request: ApprovalRequest;
    onDecision?: (decision: ReviewDecision, alwaysApprove?: boolean) => void;
    onExplain?: () => void;
}
export declare class TerminalChatCommandReview {
    private container;
    private headerBox;
    private commandBox;
    private securityBox;
    private inputBox;
    private statusBox;
    private options;
    private request;
    constructor(options: CommandReviewOptions);
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Render all components
     */
    private render;
    /**
     * Render header information
     */
    private renderHeader;
    /**
     * Render command details
     */
    private renderCommand;
    /**
     * Render security assessment
     */
    private renderSecurityAssessment;
    /**
     * Handle user decision
     */
    private handleDecision;
    /**
     * Submit decision to parent
     */
    private submitDecision;
    /**
     * Update status message
     */
    private updateStatus;
    /**
     * Show the review dialog
     */
    show(): void;
    /**
     * Hide the review dialog
     */
    hide(): void;
    /**
     * Get the container element
     */
    getElement(): blessed.Widgets.BoxElement;
    /**
     * Destroy the component
     */
    destroy(): void;
}
/**
 * Create a command review dialog
 */
export declare function createCommandReview(parent: blessed.Widgets.Node, request: ApprovalRequest, options?: Partial<CommandReviewOptions>): TerminalChatCommandReview;
/**
 * Show command review dialog and return promise
 */
export declare function showCommandReview(parent: blessed.Widgets.Node, request: ApprovalRequest): Promise<{
    decision: ReviewDecision;
    alwaysApprove?: boolean;
}>;
export default TerminalChatCommandReview;
//# sourceMappingURL=terminal-chat-command-review.d.ts.map