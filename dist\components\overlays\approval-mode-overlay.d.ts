/**
 * Approval Mode Overlay Component
 *
 * Allows users to configure command approval policies and security settings.
 */
import blessed from 'blessed';
import type { ApprovalPolicy } from '../../types/index.js';
export interface ApprovalModeOverlayOptions {
    parent: blessed.Widgets.Screen;
    currentMode: ApprovalPolicy;
    onSelect?: (mode: ApprovalPolicy) => void;
    onClose?: () => void;
}
export declare class ApprovalModeOverlay {
    private screen;
    private container;
    private modeList;
    private detailsBox;
    private statusBar;
    private modes;
    private selectedMode;
    private options;
    constructor(options: ApprovalModeOverlayOptions);
    /**
     * Initialize approval mode definitions
     */
    private initializeModes;
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Update display
     */
    private updateDisplay;
    /**
     * Update mode list
     */
    private updateModeList;
    /**
     * Update details display
     */
    private updateDetails;
    /**
     * Get use cases for mode
     */
    private getUseCases;
    /**
     * Get considerations for mode
     */
    private getConsiderations;
    /**
     * Select current mode
     */
    private selectCurrent;
    /**
     * Show the overlay
     */
    show(): void;
    /**
     * Hide the overlay
     */
    hide(): void;
    /**
     * Close the overlay
     */
    close(): void;
    /**
     * Destroy the overlay
     */
    destroy(): void;
}
export default ApprovalModeOverlay;
//# sourceMappingURL=approval-mode-overlay.d.ts.map