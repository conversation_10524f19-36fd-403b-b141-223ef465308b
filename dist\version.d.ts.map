{"version": 3, "file": "version.d.ts", "sourceRoot": "", "sources": ["../src/version.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAWH;;GAEG;AACH,wBAAgB,UAAU,IAAI,MAAM,CAenC;AAED;;GAEG;AACH,eAAO,MAAM,WAAW,QAAe,CAAC;AAExC;;GAEG;AACH,wBAAgB,cAAc,IAAI;IAChC,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;CACd,CAOA;AAED;;GAEG;AACH,wBAAgB,iBAAiB,IAAI,MAAM,CAO1C;AAGD,eAAO,MAAM,QAAQ,oBAAoB,CAAC;AAC1C,eAAO,MAAM,eAAe,+CAA+C,CAAC;AAC5E,eAAO,MAAM,UAAU,QAA2B,CAAC;AACnD,eAAO,MAAM,YAAY,QAAsC,CAAC;AAChE,eAAO,MAAM,YAAY,QAAmC,CAAC;AAG7D,eAAO,MAAM,QAAQ;;;;;;;;;;;;;;;;CAgBX,CAAC;AAGX,eAAO,MAAM,WAAW,QAAQ,CAAC;AACjC,eAAO,MAAM,gBAAgB,WAAW,CAAC;AACzC,eAAO,MAAM,mBAAmB,uCAAwC,CAAC;AAGzE,eAAO,MAAM,UAAU;;;;;CAKb,CAAC;AAGX,eAAO,MAAM,OAAO,QAAQ,CAAC;AAC7B,eAAO,MAAM,SAAS,QAA8C,CAAC;AAErE;;GAEG;AACH,wBAAgB,gBAAgB,IAAI,MAAM,CAEzC;AAED;;GAEG;AACH,wBAAgB,sBAAsB,IAAI;IACxC,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;CACd,CAYA;AAED;;GAEG;AACH,wBAAgB,WAAW,IAAI,OAAO,QAAQ,CAE7C;AAED;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,OAAO,EAAE,MAAM,OAAO,QAAQ,GAAG,OAAO,CAExE;AAED;;GAEG;AACH,wBAAgB,eAAe,IAAI;IACjC,SAAS,EAAE,OAAO,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;CACxB,CAWA;AAED;;GAEG;AACH,wBAAgB,sBAAsB,IAAI;IACxC,UAAU,EAAE,OAAO,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,CAeA;AAED;;GAEG;AACH,wBAAgB,SAAS,IAAI,MAAM,CAelC"}