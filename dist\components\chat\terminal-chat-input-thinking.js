/**
 * Terminal Chat Input Thinking Animation
 *
 * Displays animated thinking indicator while AI is processing,
 * with customizable messages and progress indication.
 */
import blessed from 'blessed';
import { Spinner } from '../vendor/spinner.js';
export class TerminalChatInputThinking {
    container;
    spinner;
    messageBox;
    progressBox;
    options;
    messages;
    currentMessageIndex = 0;
    messageInterval = null;
    isActive = false;
    startTime = 0;
    progressInterval = null;
    constructor(options) {
        this.options = options;
        this.messages = options.messages || [
            'Thinking...',
            'Processing your request...',
            'Analyzing context...',
            'Generating response...',
            'Almost ready...',
        ];
        // Create main container
        this.container = blessed.box({
            parent: options.parent,
            top: options.top || 0,
            left: options.left || 0,
            width: options.width || '100%',
            height: options.height || 3,
            style: {
                fg: 'cyan',
                bg: 'black',
            },
            hidden: true,
            tags: true,
        });
        this.createComponents();
    }
    /**
     * Create UI components
     */
    createComponents() {
        // Create spinner
        this.spinner = new Spinner({
            parent: this.container,
            top: 0,
            left: 0,
            width: 3,
            height: 1,
            type: 'dots',
            speed: 100,
            style: {
                fg: 'cyan',
            },
        });
        // Create message box
        this.messageBox = blessed.box({
            parent: this.container,
            top: 0,
            left: 3,
            width: '100%-3',
            height: 1,
            style: {
                fg: 'cyan',
            },
            content: this.messages[0],
            tags: true,
        });
        // Create progress box if enabled
        if (this.options.showProgress) {
            this.progressBox = blessed.box({
                parent: this.container,
                top: 1,
                left: 0,
                width: '100%',
                height: 1,
                style: {
                    fg: 'gray',
                },
                tags: true,
            });
        }
    }
    /**
     * Start thinking animation
     */
    start(initialMessage) {
        if (this.isActive) {
            return;
        }
        this.isActive = true;
        this.startTime = Date.now();
        this.currentMessageIndex = 0;
        // Set initial message if provided
        if (initialMessage) {
            this.messageBox.setContent(initialMessage);
        }
        else {
            this.messageBox.setContent(this.messages[0]);
        }
        // Show container and start spinner
        this.container.show();
        this.spinner.start();
        // Start message cycling
        this.startMessageCycling();
        // Start progress updates if enabled
        if (this.options.showProgress && this.progressBox) {
            this.startProgressUpdates();
        }
        this.render();
    }
    /**
     * Stop thinking animation
     */
    stop() {
        if (!this.isActive) {
            return;
        }
        this.isActive = false;
        // Stop spinner
        this.spinner.stop();
        // Clear intervals
        if (this.messageInterval) {
            clearInterval(this.messageInterval);
            this.messageInterval = null;
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
        // Hide container
        this.container.hide();
        this.render();
    }
    /**
     * Update thinking message
     */
    setMessage(message) {
        this.messageBox.setContent(message);
        this.render();
    }
    /**
     * Add a new message to the cycle
     */
    addMessage(message) {
        this.messages.push(message);
    }
    /**
     * Set progress text
     */
    setProgress(text) {
        if (this.progressBox) {
            this.progressBox.setContent(text);
            this.render();
        }
    }
    /**
     * Start message cycling
     */
    startMessageCycling() {
        if (this.messages.length <= 1) {
            return;
        }
        const interval = this.options.cycleInterval || 2000;
        this.messageInterval = setInterval(() => {
            if (!this.isActive) {
                return;
            }
            this.currentMessageIndex = (this.currentMessageIndex + 1) % this.messages.length;
            this.messageBox.setContent(this.messages[this.currentMessageIndex]);
            this.render();
        }, interval);
    }
    /**
     * Start progress updates
     */
    startProgressUpdates() {
        if (!this.progressBox) {
            return;
        }
        this.progressInterval = setInterval(() => {
            if (!this.isActive) {
                return;
            }
            const elapsed = Date.now() - this.startTime;
            const seconds = Math.floor(elapsed / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            let timeText = '';
            if (minutes > 0) {
                timeText = `${minutes}m ${remainingSeconds}s`;
            }
            else {
                timeText = `${remainingSeconds}s`;
            }
            this.progressBox.setContent(`{gray-fg}Elapsed: ${timeText}{/gray-fg}`);
            this.render();
        }, 1000);
    }
    /**
     * Render the component
     */
    render() {
        if (this.container.screen) {
            this.container.screen.render();
        }
    }
    /**
     * Check if animation is active
     */
    isThinking() {
        return this.isActive;
    }
    /**
     * Get elapsed time in milliseconds
     */
    getElapsedTime() {
        if (!this.isActive) {
            return 0;
        }
        return Date.now() - this.startTime;
    }
    /**
     * Set spinner type
     */
    setSpinnerType(type) {
        this.spinner.setType(type);
    }
    /**
     * Set spinner speed
     */
    setSpinnerSpeed(speed) {
        this.spinner.setSpeed(speed);
    }
    /**
     * Show the thinking animation
     */
    show() {
        this.container.show();
        this.render();
    }
    /**
     * Hide the thinking animation
     */
    hide() {
        this.container.hide();
        this.render();
    }
    /**
     * Get the container element
     */
    getElement() {
        return this.container;
    }
    /**
     * Destroy the component
     */
    destroy() {
        this.stop();
        this.spinner.destroy();
        this.container.destroy();
    }
}
/**
 * Create a thinking animation with default settings
 */
export function createThinkingAnimation(parent, options = {}) {
    return new TerminalChatInputThinking({
        parent,
        height: 3,
        showProgress: true,
        ...options,
    });
}
/**
 * Create a simple thinking indicator
 */
export function createSimpleThinking(parent, message = 'Thinking...') {
    return new TerminalChatInputThinking({
        parent,
        height: 1,
        messages: [message],
        showProgress: false,
    });
}
/**
 * Create thinking animation with custom messages
 */
export function createCustomThinking(parent, messages, options = {}) {
    return new TerminalChatInputThinking({
        parent,
        messages,
        height: 2,
        showProgress: true,
        cycleInterval: 1500,
        ...options,
    });
}
export default TerminalChatInputThinking;
//# sourceMappingURL=terminal-chat-input-thinking.js.map