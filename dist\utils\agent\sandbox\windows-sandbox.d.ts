/**
 * Windows Sandbox Implementation
 *
 * Provides Windows-specific sandboxing using Job Objects,
 * process isolation, and security restrictions.
 */
import { BaseSandbox, type SandboxCapabilities } from './index.js';
import type { ExecInput, ExecResult } from '../../../types/index.js';
export declare class WindowsSandbox extends BaseSandbox {
    private activeProcesses;
    /**
     * Execute command in Windows sandbox
     */
    execute(input: ExecInput): Promise<ExecResult>;
    /**
     * Get Windows sandbox capabilities
     */
    getCapabilities(): SandboxCapabilities;
    /**
     * Check if Windows sandbox is available
     */
    isAvailable(): Promise<boolean>;
    /**
     * Setup Windows sandbox environment
     */
    setup(): Promise<void>;
    /**
     * Cleanup Windows sandbox resources
     */
    cleanup(): Promise<void>;
    /**
     * Prepare command for Windows execution
     */
    private prepareWindowsCommand;
    /**
     * Kill process and its children
     */
    private killProcess;
    /**
     * Apply Windows-specific security restrictions
     */
    private applySecurityRestrictions;
    /**
     * Check if path is safe for access
     */
    private isPathSafe;
    /**
     * Get Windows version information
     */
    private getWindowsVersion;
    /**
     * Check if running with administrator privileges
     */
    private isElevated;
}
export default WindowsSandbox;
//# sourceMappingURL=windows-sandbox.d.ts.map