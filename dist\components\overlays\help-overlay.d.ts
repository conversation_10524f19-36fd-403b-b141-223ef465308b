/**
 * Help Overlay Component
 *
 * Displays comprehensive help information including keyboard shortcuts,
 * slash commands, and usage instructions.
 */
import blessed from 'blessed';
export interface HelpOverlayOptions {
    parent: blessed.Widgets.Screen;
    onClose?: () => void;
}
export declare class HelpOverlay {
    private screen;
    private container;
    private contentBox;
    private options;
    constructor(options: HelpOverlayOptions);
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Build help content
     */
    private buildContent;
    /**
     * Show the overlay
     */
    show(): void;
    /**
     * Hide the overlay
     */
    hide(): void;
    /**
     * Close the overlay
     */
    close(): void;
    /**
     * Destroy the overlay
     */
    destroy(): void;
}
export default HelpOverlay;
//# sourceMappingURL=help-overlay.d.ts.map