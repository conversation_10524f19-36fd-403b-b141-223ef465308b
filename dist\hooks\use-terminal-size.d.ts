/**
 * Terminal Size Hook
 *
 * Manages terminal size detection and resize events with proper cleanup
 * and fallback values for different environments.
 */
import { EventEmitter } from 'events';
export interface TerminalSize {
    columns: number;
    rows: number;
}
declare class TerminalSizeManager extends EventEmitter {
    private currentSize;
    private listeners;
    private resizeHandler;
    constructor();
    /**
     * Get current terminal size
     */
    getSize(): TerminalSize;
    /**
     * Add size change listener
     */
    addListener(callback: (size: TerminalSize) => void): () => void;
    /**
     * Detect current terminal size
     */
    private detectSize;
    /**
     * Setup resize event handler
     */
    private setupResizeHandler;
    /**
     * Notify all listeners of size change
     */
    private notifyListeners;
    /**
     * Cleanup resources
     */
    cleanup(): void;
}
declare const terminalSizeManager: TerminalSizeManager;
/**
 * Terminal size hook
 */
export declare function useTerminalSize(): {
    columns: number;
    rows: number;
    addListener: (callback: (size: TerminalSize) => void) => () => void;
};
/**
 * Get terminal size with custom padding
 */
export declare function getTerminalSize(paddingX?: number, paddingY?: number): TerminalSize;
/**
 * Check if terminal is wide enough for feature
 */
export declare function isTerminalWideEnough(minColumns?: number): boolean;
/**
 * Check if terminal is tall enough for feature
 */
export declare function isTerminalTallEnough(minRows?: number): boolean;
/**
 * Get optimal layout dimensions
 */
export declare function getOptimalLayout(): {
    chatHeight: number;
    inputHeight: number;
    statusHeight: number;
    sidebarWidth: number;
};
/**
 * Cleanup terminal size manager
 */
export declare function cleanupTerminalSize(): void;
export default terminalSizeManager;
//# sourceMappingURL=use-terminal-size.d.ts.map