{"version": 3, "file": "help-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/help-overlay.tsx"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAO/D,MAAM,OAAO,WAAW;IACd,MAAM,CAAyB;IAC/B,SAAS,CAA6B;IACtC,UAAU,CAA6B;IACvC,OAAO,CAAqB;IAEpC,YAAY,OAA2B;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,iBAAiB;QACjB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,KAAK;YACb,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM;aACb;YACD,KAAK,EAAE;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,MAAM;iBACX;aACF;YACD,KAAK,EAAE,0BAA0B;YACjC,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,IAAI;SACT,CAAC,CAAC;QAEH,cAAc;QACd,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;YAC5B,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;YACd,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,SAAS,EAAE;gBACT,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE;oBACL,EAAE,EAAE,MAAM;iBACX;gBACD,KAAK,EAAE;oBACL,OAAO,EAAE,IAAI;iBACd;aACF;YACD,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,IAAI;YACR,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,iBAAiB;QACjB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE;YACvC,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE;YACtC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;YACpC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,GAAG,EAAE;YACrC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE;YACnC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE;YACjC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,MAAM,OAAO,GAAG;YACd,2FAA2F;YAC3F,EAAE;YACF,0CAA0C;YAC1C,8EAA8E;YAC9E,2EAA2E;YAC3E,EAAE;YACF,6CAA6C;YAC7C,6CAA6C;YAC7C,kDAAkD;YAClD,wDAAwD;YACxD,oDAAoD;YACpD,EAAE;YACF,oDAAoD;YACpD,EAAE;YACF,gCAAgC;YAChC,sDAAsD;YACtD,oCAAoC;YACpC,gCAAgC;YAChC,sCAAsC;YACtC,sCAAsC;YACtC,uCAAuC;YACvC,EAAE;YACF,+BAA+B;YAC/B,oEAAoE;YACpE,kDAAkD;YAClD,8CAA8C;YAC9C,0CAA0C;YAC1C,2CAA2C;YAC3C,qCAAqC;YACrC,uCAAuC;YACvC,oCAAoC;YACpC,EAAE;YACF,0BAA0B;YAC1B,6BAA6B;YAC7B,2BAA2B;YAC3B,oCAAoC;YACpC,kCAAkC;YAClC,2BAA2B;YAC3B,8BAA8B;YAC9B,EAAE;YACF,gDAAgD;YAChD,EAAE;YACF,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC1B,gBAAgB,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,GAAG,CAAC,WAAW,EAAE,CACxE;YACD,EAAE;YACF,iDAAiD;YACjD,EAAE;YACF,mDAAmD;YACnD,qDAAqD;YACrD,+CAA+C;YAC/C,gDAAgD;YAChD,EAAE;YACF,qEAAqE;YACrE,+DAA+D;YAC/D,EAAE;YACF,gDAAgD;YAChD,EAAE;YACF,oEAAoE;YACpE,wEAAwE;YACxE,kEAAkE;YAClE,EAAE;YACF,oDAAoD;YACpD,EAAE;YACF,yBAAyB;YACzB,iCAAiC;YACjC,gBAAgB;YAChB,iBAAiB;YACjB,yBAAyB;YACzB,cAAc;YACd,YAAY;YACZ,cAAc;YACd,QAAQ;YACR,WAAW;YACX,cAAc;YACd,EAAE;YACF,8DAA8D;YAC9D,EAAE;YACF,+CAA+C;YAC/C,EAAE;YACF,qCAAqC;YACrC,uCAAuC;YACvC,wCAAwC;YACxC,EAAE;YACF,wBAAwB;YACxB,2CAA2C;YAC3C,yCAAyC;YACzC,0CAA0C;YAC1C,EAAE;YACF,mDAAmD;YACnD,EAAE;YACF,qCAAqC;YACrC,iCAAiC;YACjC,sCAAsC;YACtC,+BAA+B;YAC/B,mCAAmC;YACnC,oCAAoC;YACpC,yBAAyB;YACzB,qBAAqB;YACrB,EAAE;YACF,0CAA0C;YAC1C,EAAE;YACF,cAAc;YACd,6CAA6C;YAC7C,0DAA0D;YAC1D,oCAAoC;YACpC,EAAE;YACF,gBAAgB;YAChB,2CAA2C;YAC3C,uCAAuC;YACvC,8BAA8B;YAC9B,iCAAiC;YACjC,EAAE;YACF,iDAAiD;YACjD,EAAE;YACF,oCAAoC;YACpC,kDAAkD;YAClD,4CAA4C;YAC5C,+DAA+D;YAC/D,iDAAiD;YACjD,EAAE;YACF,0EAA0E;SAC3E,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,IAAI;QACF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAI;QACF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;CACF;AAED,eAAe,WAAW,CAAC"}